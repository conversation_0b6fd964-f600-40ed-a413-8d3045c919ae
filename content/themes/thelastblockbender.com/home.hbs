{{!< default}}


<style>
.grid-container {
  display: grid;
  grid-template-columns: repeat(16, 1fr);
  grid-template-rows: repeat(16, 1fr);
  gap: 22px 22px;
}

.grid-item {
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: 600;
  position: relative;
  overflow: visible;
}


.item-1 {
  grid-column: 5 / 13;
  grid-row: 7 / 15;
  --hover-image: url('meow.png');
}

.item-3 {
  grid-column: 14 / 17;
  grid-row: 1 / 7;
  --hover-image: url('meow.png');
}

.item-6 {
  grid-column: 1 / 5;
  grid-row: 7 / 20;
  --hover-image: url('meow.png');
}

.item-7 {
  grid-column: 1 / 8;
  grid-row: 20 / 26;
  --hover-image: url('meow.png');
}

.item-8 {
  grid-column: 5 / 7;
  grid-row: 15 / 20;
  --hover-image: url('meow.png');
}

.item-9 {
  grid-column: 7 / 9;
  grid-row: 15 / 20;
  --hover-image: url('meow.png');
}

.item-10 {
  grid-column: 9 / 11;
  grid-row: 15 / 20;
  --hover-image: url('meow.png');
}

.item-11 {
  grid-column: 11 / 13;
  grid-row: 15 / 20;
  --hover-image: url('meow.png');
}

.item-14 {
  grid-column: 8 / 13;
  grid-row: 20 / 26;
  --hover-image: url('meow.png');
}

.item-15 {
  grid-column: 13 / 17;
  grid-row: 12 / 26;
  --hover-image: url('meow.png');
}

.item-16 {
  grid-column: 13 / 17;
  grid-row: 7 / 12;
  --hover-image: url('meow.png');
}

.item-17 {
  grid-column: 7 / 14;
  grid-row: 1 / 7;
  --hover-image: url('meow.png');
}

.item-18 {
  grid-column: 1 / 7;
  grid-row: 1 / 4;
  --hover-image: url('meow.png');
}

.item-19 {
  grid-column: 1 / 7;
  grid-row: 4 / 7;
  --hover-image: url('meow.png');
}


/* custom overrides */


.grid-container {
    max-width: 1200px;
    margin: 0 auto;
}

body {
  background: url('grad.jpeg');
  background-size: cover;
}

.grid-item.social-button {
    padding: 16px 32px;
}

@media(max-width:1024px) {
   .grid-item.social-button {
    padding: 0px;
} 
}

.social-button img {
    margin: 0px 0px;
    opacity: 0.6;
}

.social-button:hover img {
    opacity: 1;
}

.java, .bedrock {
    display: block;
    text-align: center;
}

.java h2, .bedrock h2 {
    font-weight: 900;
    font-size: 26px;
    margin-bottom: 15px;
}
.java h4, .bedrock h4 {
    font-size: 14px;
    font-weight: 200;
}

/* Tooltip styles */
.copy-tooltip {
    position: absolute;
    bottom: 25%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 16px;
    font-weight: 400;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: opacity 0.3s ease, visibility 0.3s ease;
    z-index: 1000;
    pointer-events: none;
}

.copy-tooltip::after {
    content: '';
    position: absolute;
    top: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.8);
}

.copy-tooltip.show {
    opacity: 1;
    visibility: visible;
}

.grid-item.disabled {
    background: rgba(255, 255, 255, 0.02);
    border: 1px solid rgba(255, 255, 255, 0.1);

}

.grid-item.disabled h2, .grid-item.disabled h4 {
        color: rgba(255,255,255,0.4) !important;
}




.grid-item.item-1 img {
position: absolute;
 top: -40%;
width: 150%;
    max-width: none;
}
</style>



<main id="main">
    <div class="grid-container">
    <div class="grid-item item-1" data-link="/">
        <img src="http://localhost:2368/content/images/2025/07/pow.png" alt="The Last BlockBender">The Last BlockBender<br> <h5>{{> server-players}}</h5>
    </div>
    <div class="grid-item glass-button item-3" data-link="/how-to-bend">
        How to Bend
    </div>
    <div class="grid-item glass-button item-6" data-link="/wiki">
        Wiki
    </div>
    <div class="grid-item glass-button item-7" data-link="https://discord.thelastblockbender.com">
        <h5>{{> discord-members}}</h5>
    </div>
    <div class="grid-item social-button item-8" data-link="https://www.instagram.com/thelastblockbendermc/">
       <img src="{{asset 'images/instagram.png'}}" alt-"Instagram">
    </div>
    <div class="grid-item social-button item-9" data-link="https://www.youtube.com/@lastblockbender">
       <img src="{{asset 'images/youtube.png'}}" alt-"Youtube">
    </div>
    <div class="grid-item social-button item-10" data-link="https://www.tiktok.com/@lastblockbender">
       <img src="{{asset 'images/tiktok.png'}}" alt-"Tiktok">
    </div>
    <div class="grid-item social-button item-11" data-link="https://ca.pinterest.com/thelastblockbender/">
       <img src="{{asset 'images/pinterest.png'}}" alt-"Pinterest">
    </div>
    <div class="grid-item glass-button item-14" data-link="/vote">
        Vote
    </div>
    <div class="grid-item glass-button item-15" data-link="https://store.thelastblockbender.com">
        Store
    </div>
    <div class="grid-item glass-button item-16" data-link="/faq">
        FAQ
    </div>
    <div class="grid-item glass-button item-17" data-link="/how-to-join">
        How to Join
    </div>
    <div class="grid-item glass-button java item-18">
        <h2>play.blockbender.com</h2>
        <h4>Java Edition</h4>
        <div class="copy-tooltip">Copied to clipboard</div>
    </div>
    <div class="grid-item glass-button bedrock item-19">
        <h2>Port 19132</h2>
        <h4>Bedrock Edition</h4>
        <div class="copy-tooltip">Copied to clipboard</div>
    </div>
    </div>
</main>

<script>
document.body.classList.add('darkMode');

// Copy Java server text on click
document.querySelector('.java').addEventListener('click', function() {
    const h2Text = this.querySelector('h2').textContent;
    const tooltip = this.querySelector('.copy-tooltip');

    navigator.clipboard.writeText(h2Text).then(() => {
        // Show tooltip and add disabled class
        tooltip.classList.add('show');
        this.classList.add('disabled');

        // Hide tooltip and remove disabled class after 2 seconds
        setTimeout(() => {
            tooltip.classList.remove('show');
            this.classList.remove('disabled');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy text: ', err);
    });
});

// Copy Bedrock server text on click
document.querySelector('.bedrock').addEventListener('click', function() {
    const tooltip = this.querySelector('.copy-tooltip');

    navigator.clipboard.writeText("19132").then(() => {
        // Show tooltip and add disabled class
        tooltip.classList.add('show');
        this.classList.add('disabled');

        // Hide tooltip and remove disabled class after 2 seconds
        setTimeout(() => {
            tooltip.classList.remove('show');
            this.classList.remove('disabled');
        }, 2000);
    }).catch(err => {
        console.error('Failed to copy text: ', err);
    });
});

// Handle grid item link clicks
document.querySelectorAll('.grid-item[data-link]').forEach(item => {
    item.addEventListener('click', function() {
        const link = this.getAttribute('data-link');
        if (link) {
            // Check if it's an external link (starts with http)
            if (link.startsWith('http')) {
                window.open(link, '_blank');
            } else {
                window.location.href = link;
            }
        }
    });
});
</script>
