function dropdown(){let n=window.matchMedia("(max-width: 767px)"),o=document.querySelector(".gh-head-menu"),s=o?.querySelector(".nav");if(s){let t=document.querySelector(".gh-head-logo"),e=s.innerHTML;n.matches&&s.querySelectorAll("li").forEach(function(t,e){t.style.transitionDelay=.03*(e+1)+"s"});let i=function(){if(!n.matches){for(var t=[];s.offsetWidth+64>o.offsetWidth&&s.lastElementChild;)t.unshift(s.lastElementChild),s.lastElementChild.remove();if(t.length){let e=document.createElement("button"),i=(e.setAttribute("class","nav-more-toggle gh-icon-btn"),e.setAttribute("aria-label","More"),e.innerHTML='<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" fill="currentColor"><path d="M21.333 16c0-1.473 1.194-2.667 2.667-2.667v0c1.473 0 2.667 1.194 2.667 2.667v0c0 1.473-1.194 2.667-2.667 2.667v0c-1.473 0-2.667-1.194-2.667-2.667v0zM13.333 16c0-1.473 1.194-2.667 2.667-2.667v0c1.473 0 2.667 1.194 2.667 2.667v0c0 1.473-1.194 2.667-2.667 2.667v0c-1.473 0-2.667-1.194-2.667-2.667v0zM5.333 16c0-1.473 1.194-2.667 2.667-2.667v0c1.473 0 2.667 1.194 2.667 2.667v0c0 1.473-1.194 2.667-2.667 2.667v0c-1.473 0-2.667-1.194-2.667-2.667v0z"></path></svg>',document.createElement("div"));i.setAttribute("class","gh-dropdown"),10<=t.length?(document.body.classList.add("is-dropdown-mega"),i.style.gridTemplateRows=`repeat(${Math.ceil(t.length/2)}, 1fr)`):document.body.classList.remove("is-dropdown-mega"),t.forEach(function(t){i.appendChild(t)}),e.appendChild(i),s.appendChild(e),document.body.classList.add("is-dropdown-loaded"),window.addEventListener("click",function(t){document.body.classList.contains("is-dropdown-open")?document.body.classList.remove("is-dropdown-open"):e.contains(t.target)&&document.body.classList.add("is-dropdown-open")})}else document.body.classList.add("is-dropdown-loaded")}};imagesLoaded(t,function(){i()}),window.addEventListener("load",function(){t||i()}),window.addEventListener("resize",function(){setTimeout(()=>{s.innerHTML=e,i()},1)})}}function lightbox(t){document.querySelectorAll(t).forEach(function(t){t.addEventListener("click",function(t){var e=t;e.preventDefault();for(var i,n=[],o=0,s=e.target.closest(".kg-card").previousElementSibling;s&&(s.classList.contains("kg-image-card")||s.classList.contains("kg-gallery-card"));){var r=[];s.querySelectorAll("img").forEach(function(t){r.push({src:t.getAttribute("src"),msrc:t.getAttribute("src"),w:t.getAttribute("width"),h:t.getAttribute("height"),el:t}),o+=1}),s=s.previousElementSibling,n=r.concat(n)}e.target.classList.contains("kg-image")?n.push({src:e.target.getAttribute("src"),msrc:e.target.getAttribute("src"),w:e.target.getAttribute("width"),h:e.target.getAttribute("height"),el:e.target}):(i=!1,e.target.closest(".kg-gallery-card").querySelectorAll("img").forEach(function(t){n.push({src:t.getAttribute("src"),msrc:t.getAttribute("src"),w:t.getAttribute("width"),h:t.getAttribute("height"),el:t}),i||t===e.target?i=!0:o+=1}));for(var a=e.target.closest(".kg-card").nextElementSibling;a&&(a.classList.contains("kg-image-card")||a.classList.contains("kg-gallery-card"));)a.querySelectorAll("img").forEach(function(t){n.push({src:t.getAttribute("src"),msrc:t.getAttribute("src"),w:t.getAttribute("width"),h:t.getAttribute("height"),el:t})}),a=a.nextElementSibling;t=document.querySelectorAll(".pswp")[0];new PhotoSwipe(t,PhotoSwipeUI_Default,n,{bgOpacity:.9,closeOnScroll:!0,fullscreenEl:!1,history:!1,index:o,shareEl:!1,zoomEl:!1,getThumbBoundsFn:function(t){var t=n[t].el,e=window.pageYOffset||document.documentElement.scrollTop,t=t.getBoundingClientRect();return{x:t.left,y:t.top+e,w:t.width}}}).init()})})}function pagination(t,a,l=!1){let c=document.querySelector(".gh-feed");if(!c)return;let e=!1,i=c.nextElementSibling||c.parentElement.nextElementSibling||document.querySelector(".gh-foot"),h=document.querySelector(".gh-loadmore"),n=(!document.querySelector("link[rel=next]")&&h&&h.remove(),async function(){var n=document.querySelector("link[rel=next]");if(n)try{var t=await(await fetch(n.href)).text(),o=(new DOMParser).parseFromString(t,"text/html"),s=o.querySelectorAll(".gh-feed:not(.gh-featured):not(.gh-related) > *");let e=document.createDocumentFragment(),i=[];s.forEach(function(t){t=document.importNode(t,!0);l&&(t.style.visibility="hidden"),e.appendChild(t),i.push(t)}),c.appendChild(e),a&&a(i,u);var r=o.querySelector("link[rel=next]");r&&r.href?n.href=r.href:(n.remove(),h&&h.remove())}catch(t){throw n.remove(),h&&h.remove(),t}}),u=async function(){i.getBoundingClientRect().top<=window.innerHeight&&document.querySelector("link[rel=next]")&&await n()};let o=new IntersectionObserver(async function(t){if(!e){if(e=!0,t[0].isIntersecting)if(l)await n();else for(;i.getBoundingClientRect().top<=window.innerHeight&&document.querySelector("link[rel=next]");)await n();e=!1,document.querySelector("link[rel=next]")||o.disconnect()}});t?o.observe(i):h.addEventListener("click",n)}((t,e)=>{"object"==typeof module&&module.exports?module.exports=e():t.EvEmitter=e()})("undefined"!=typeof window?window:this,function(){function t(){}var e=t.prototype;return e.on=function(t,e){var i;return t&&e&&((i=(i=this._events=this._events||{})[t]=i[t]||[]).includes(e)||i.push(e)),this},e.once=function(t,e){var i;return t&&e&&(this.on(t,e),((i=this._onceEvents=this._onceEvents||{})[t]=i[t]||{})[e]=!0),this},e.off=function(t,e){t=this._events&&this._events[t];return t&&t.length&&-1!=(e=t.indexOf(e))&&t.splice(e,1),this},e.emitEvent=function(t,e){let i=this._events&&this._events[t];if(i&&i.length){i=i.slice(0),e=e||[];var n,o=this._onceEvents&&this._onceEvents[t];for(n of i)o&&o[n]&&(this.off(t,n),delete o[n]),n.apply(this,e)}return this},e.allOff=function(){return delete this._events,delete this._onceEvents,this},t}),((t,e)=>{"object"==typeof module&&module.exports?module.exports=e(t,require("ev-emitter")):t.imagesLoaded=e(t,t.EvEmitter)})("undefined"!=typeof window?window:this,function(e,t){let s=e.jQuery,r=e.console;function a(t,e,i){if(!(this instanceof a))return new a(t,e,i);let n=t,o;(n="string"==typeof t?document.querySelectorAll(t):n)?(this.elements=(o=n,Array.isArray(o)?o:"object"==typeof o&&"number"==typeof o.length?[...o]:[o]),this.options={},"function"==typeof e?i=e:Object.assign(this.options,e),i&&this.on("always",i),this.getImages(),s&&(this.jqDeferred=new s.Deferred),setTimeout(this.check.bind(this))):r.error("Bad element for imagesLoaded "+(n||t))}(a.prototype=Object.create(t.prototype)).getImages=function(){this.images=[],this.elements.forEach(this.addElementImages,this)};let o=[1,9,11],l=(a.prototype.addElementImages=function(t){"IMG"===t.nodeName&&this.addImage(t),!0===this.options.background&&this.addElementBackgroundImages(t);var e,i,n=t.nodeType;if(n&&o.includes(n)){for(e of t.querySelectorAll("img"))this.addImage(e);if("string"==typeof this.options.background)for(i of t.querySelectorAll(this.options.background))this.addElementBackgroundImages(i)}},/url\((['"])?(.*?)\1\)/gi);function i(t){this.img=t}function n(t,e){this.url=t,this.element=e,this.img=new Image}return a.prototype.addElementBackgroundImages=function(e){var i=getComputedStyle(e);if(i){let t=l.exec(i.backgroundImage);for(;null!==t;){var n=t&&t[2];n&&this.addBackground(n,e),t=l.exec(i.backgroundImage)}}},a.prototype.addImage=function(t){t=new i(t);this.images.push(t)},a.prototype.addBackground=function(t,e){t=new n(t,e);this.images.push(t)},a.prototype.check=function(){if(this.progressedCount=0,this.hasAnyBroken=!1,this.images.length){let e=(t,e,i)=>{setTimeout(()=>{this.progress(t,e,i)})};this.images.forEach(function(t){t.once("progress",e),t.check()})}else this.complete()},a.prototype.progress=function(t,e,i){this.progressedCount++,this.hasAnyBroken=this.hasAnyBroken||!t.isLoaded,this.emitEvent("progress",[this,t,e]),this.jqDeferred&&this.jqDeferred.notify&&this.jqDeferred.notify(this,t),this.progressedCount===this.images.length&&this.complete(),this.options.debug&&r&&r.log("progress: "+i,t,e)},a.prototype.complete=function(){var t=this.hasAnyBroken?"fail":"done";this.isComplete=!0,this.emitEvent(t,[this]),this.emitEvent("always",[this]),this.jqDeferred&&(t=this.hasAnyBroken?"reject":"resolve",this.jqDeferred[t](this))},(i.prototype=Object.create(t.prototype)).check=function(){this.getIsImageComplete()?this.confirm(0!==this.img.naturalWidth,"naturalWidth"):(this.proxyImage=new Image,this.img.crossOrigin&&(this.proxyImage.crossOrigin=this.img.crossOrigin),this.proxyImage.addEventListener("load",this),this.proxyImage.addEventListener("error",this),this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.proxyImage.src=this.img.currentSrc||this.img.src)},i.prototype.getIsImageComplete=function(){return this.img.complete&&this.img.naturalWidth},i.prototype.confirm=function(t,e){this.isLoaded=t;t=this.img.parentNode,t="PICTURE"===t.nodeName?t:this.img;this.emitEvent("progress",[this,t,e])},i.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},i.prototype.onload=function(){this.confirm(!0,"onload"),this.unbindEvents()},i.prototype.onerror=function(){this.confirm(!1,"onerror"),this.unbindEvents()},i.prototype.unbindEvents=function(){this.proxyImage.removeEventListener("load",this),this.proxyImage.removeEventListener("error",this),this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},(n.prototype=Object.create(i.prototype)).check=function(){this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.img.src=this.url,this.getIsImageComplete()&&(this.confirm(0!==this.img.naturalWidth,"naturalWidth"),this.unbindEvents())},n.prototype.unbindEvents=function(){this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},n.prototype.confirm=function(t,e){this.isLoaded=t,this.emitEvent("progress",[this,this.element,e])},(a.makeJQueryPlugin=function(t){(t=t||e.jQuery)&&((s=t).fn.imagesLoaded=function(t,e){return new a(this,t,e).jqDeferred.promise(s(this))})})(),a}),((t,e)=>{"function"==typeof define&&define.amd?define(e):"object"==typeof exports?module.exports=e():t.PhotoSwipeUI_Default=e()})(this,function(){return function(n,a){function t(t){if(I)return!0;t=t||window.event,E.timeToIdle&&E.mouseUsed&&!v&&r();for(var e,i,n=(t.target||t.srcElement).getAttribute("class")||"",o=0;o<O.length;o++)(e=O[o]).onTap&&-1<n.indexOf("pswp__"+e.name)&&(e.onTap(),i=!0);i&&(t.stopPropagation&&t.stopPropagation(),I=!0,t=a.features.isOldAndroid?600:30,setTimeout(function(){I=!1},t))}function i(){var t=1===E.getNumItemsFn();t!==C&&(z(u,"ui--one-slide",t),C=t)}function e(){z(f,"share-modal--hidden",$)}function o(){for(var t,e,i,n,o="",s=0;s<E.shareButtons.length;s++)t=E.shareButtons[s],e=E.getImageURLForShare(t),i=E.getPageURLForShare(t),n=E.getTextForShare(t),o+='<a href="'+t.url.replace("{{url}}",encodeURIComponent(i)).replace("{{image_url}}",encodeURIComponent(e)).replace("{{raw_image_url}}",e).replace("{{text}}",encodeURIComponent(n))+'" target="_blank" class="pswp__share--'+t.id+'"'+(t.download?"download":"")+">"+t.label+"</a>",E.parseShareButtonOut&&(o=E.parseShareButtonOut(t,o));f.children[0].innerHTML=o,f.children[0].onclick=F}function s(t){for(var e=0;e<E.closeElClasses.length;e++)if(a.hasClass(t,"pswp__"+E.closeElClasses[e]))return!0}function r(){clearTimeout(S),L=0,v&&A.setIdle(!1)}function l(t){(t=(t=t||window.event).relatedTarget||t.toElement)&&"HTML"!==t.nodeName||(clearTimeout(S),S=setTimeout(function(){A.setIdle(!0)},E.timeToIdleOutside))}function c(t){_!==t&&(z(x,"preloader--active",!t),_=t)}function P(t){var e,i=t.vGap;!n.likelyTouchDevice||E.mouseUsed||screen.width>E.fitControlsWidth?(e=E.barsSize,E.captionEl&&"auto"===e.bottom?(p||((p=a.createEl("pswp__caption pswp__caption--fake")).appendChild(a.createEl("pswp__caption__center")),u.insertBefore(p,d),a.addClass(u,"pswp__ui--fit")),E.addCaptionHTMLFn(t,p,!0)?(t=p.clientHeight,i.bottom=parseInt(t,10)||44):i.bottom=e.top):i.bottom="auto"===e.bottom?0:e.bottom,i.top=e.top):i.top=i.bottom=0}function j(){function t(t){if(t)for(var e=t.length,i=0;i<e;i++){o=t[i],s=o.className;for(var n=0;n<O.length;n++)r=O[n],-1<s.indexOf("pswp__"+r.name)&&(E[r.option]?(a.removeClass(o,"pswp__element--disabled"),r.onInit&&r.onInit(o)):a.addClass(o,"pswp__element--disabled"))}}t(u.children);var o,s,r,e=a.getChildByClass(u,"pswp__top-bar");e&&t(e.children)}var h,u,d,p,m,g,f,y,v,w,x,_,b,C,E,I,T,S,A=this,k=!1,D=!0,$=!0,R={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(t,e){return t.title?(e.children[0].innerHTML=t.title,!0):(e.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"download",label:"Download image",url:"{{raw_image_url}}",download:!0}],getImageURLForShare:function(){return n.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return n.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},z=function(t,e,i){a[(i?"add":"remove")+"Class"](t,"pswp__"+e)},M=function(){return($=!$)?(a.removeClass(f,"pswp__share-modal--fade-in"),setTimeout(function(){$&&e()},300)):(e(),setTimeout(function(){$||a.addClass(f,"pswp__share-modal--fade-in")},30)),$||o(),!1},F=function(t){var e=(t=t||window.event).target||t.srcElement;return n.shout("shareLinkClick",t,e),!(!e.href||!e.hasAttribute("download")&&(window.open(e.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),$||M(),1))},L=0,O=[{name:"caption",option:"captionEl",onInit:function(t){d=t}},{name:"share-modal",option:"shareEl",onInit:function(t){f=t},onTap:function(){M()}},{name:"button--share",option:"shareEl",onInit:function(t){g=t},onTap:function(){M()}},{name:"button--zoom",option:"zoomEl",onTap:n.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(t){m=t}},{name:"button--close",option:"closeEl",onTap:n.close},{name:"button--arrow--left",option:"arrowEl",onTap:n.prev},{name:"button--arrow--right",option:"arrowEl",onTap:n.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){h.isFullscreen()?h.exit():h.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(t){x=t}}];A.init=function(){var e;a.extend(n.options,R,!0),E=n.options,u=a.getChildByClass(n.scrollWrap,"pswp__ui"),(w=n.listen)("onVerticalDrag",function(t){D&&t<.95?A.hideControls():!D&&.95<=t&&A.showControls()}),w("onPinchClose",function(t){D&&t<.9?(A.hideControls(),e=!0):e&&!D&&.9<t&&A.showControls()}),w("zoomGestureEnded",function(){(e=!1)&&!D&&A.showControls()}),w("beforeChange",A.update),w("doubleTap",function(t){var e=n.currItem.initialZoomLevel;n.getZoomLevel()!==e?n.zoomTo(e,t,333):n.zoomTo(E.getDoubleTapZoom(!1,n.currItem),t,333)}),w("preventDragEvent",function(t,e,i){var n=t.target||t.srcElement;n&&n.getAttribute("class")&&-1<t.type.indexOf("mouse")&&(0<n.getAttribute("class").indexOf("__caption")||/(SMALL|STRONG|EM)/i.test(n.tagName))&&(i.prevent=!1)}),w("bindEvents",function(){a.bind(u,"pswpTap click",t),a.bind(n.scrollWrap,"pswpTap",A.onGlobalTap),n.likelyTouchDevice||a.bind(n.scrollWrap,"mouseover",A.onMouseOver)}),w("unbindEvents",function(){$||M(),T&&clearInterval(T),a.unbind(document,"mouseout",l),a.unbind(document,"mousemove",r),a.unbind(u,"pswpTap click",t),a.unbind(n.scrollWrap,"pswpTap",A.onGlobalTap),a.unbind(n.scrollWrap,"mouseover",A.onMouseOver),h&&(a.unbind(document,h.eventK,A.updateFullscreen),h.isFullscreen()&&(E.hideAnimationDuration=0,h.exit()),h=null)}),w("destroy",function(){E.captionEl&&(p&&u.removeChild(p),a.removeClass(d,"pswp__caption--empty")),f&&(f.children[0].onclick=null),a.removeClass(u,"pswp__ui--over-close"),a.addClass(u,"pswp__ui--hidden"),A.setIdle(!1)}),E.showAnimationDuration||a.removeClass(u,"pswp__ui--hidden"),w("initialZoomIn",function(){E.showAnimationDuration&&a.removeClass(u,"pswp__ui--hidden")}),w("initialZoomOut",function(){a.addClass(u,"pswp__ui--hidden")}),w("parseVerticalMargin",P),j(),E.shareEl&&g&&f&&($=!0),i(),E.timeToIdle&&w("mouseUsed",function(){a.bind(document,"mousemove",r),a.bind(document,"mouseout",l),T=setInterval(function(){2===++L&&A.setIdle(!0)},E.timeToIdle/2)}),E.fullscreenEl&&!a.features.isOldAndroid&&((h=h||A.getFullscreenAPI())?(a.bind(document,h.eventK,A.updateFullscreen),A.updateFullscreen(),a.addClass(n.template,"pswp--supports-fs")):a.removeClass(n.template,"pswp--supports-fs")),E.preloaderEl&&(c(!0),w("beforeChange",function(){clearTimeout(b),b=setTimeout(function(){n.currItem&&n.currItem.loading?n.allowProgressiveImg()&&(!n.currItem.img||n.currItem.img.naturalWidth)||c(!1):c(!0)},E.loadingIndicatorDelay)}),w("imageLoadComplete",function(t,e){n.currItem===e&&c(!0)}))},A.setIdle=function(t){z(u,"ui--idle",v=t)},A.update=function(){k=!(!D||!n.currItem||(A.updateIndexIndicator(),E.captionEl&&(E.addCaptionHTMLFn(n.currItem,d),z(d,"caption--empty",!n.currItem.title)),0)),$||M(),i()},A.updateFullscreen=function(t){t&&setTimeout(function(){n.setScrollOffset(0,a.getScrollY())},50),a[(h.isFullscreen()?"add":"remove")+"Class"](n.template,"pswp--fs")},A.updateIndexIndicator=function(){E.counterEl&&(m.innerHTML=n.getCurrentIndex()+1+E.indexIndicatorSep+E.getNumItemsFn())},A.onGlobalTap=function(t){var e=(t=t||window.event).target||t.srcElement;if(!I)if(t.detail&&"mouse"===t.detail.pointerType)s(e)?n.close():a.hasClass(e,"pswp__img")&&(1===n.getZoomLevel()&&n.getZoomLevel()<=n.currItem.fitRatio?E.clickToCloseNonZoomable&&n.close():n.toggleDesktopZoom(t.detail.releasePoint));else if(E.tapToToggleControls&&(D?A.hideControls():A.showControls()),E.tapToClose&&(a.hasClass(e,"pswp__img")||s(e)))return void n.close()},A.onMouseOver=function(t){t=(t=t||window.event).target||t.srcElement;z(u,"ui--over-close",s(t))},A.hideControls=function(){a.addClass(u,"pswp__ui--hidden"),D=!1},A.showControls=function(){D=!0,k||A.update(),a.removeClass(u,"pswp__ui--hidden")},A.supportsFullscreen=function(){var t=document;return!!(t.exitFullscreen||t.mozCancelFullScreen||t.webkitExitFullscreen||t.msExitFullscreen)},A.getFullscreenAPI=function(){var t,e=document.documentElement,i="fullscreenchange";return e.requestFullscreen?t={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:i}:e.mozRequestFullScreen?t={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+i}:e.webkitRequestFullscreen?t={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+i}:e.msRequestFullscreen&&(t={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),t&&(t.enter=function(){return y=E.closeOnScroll,E.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK?n.template[this.enterK]():void n.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},t.exit=function(){return E.closeOnScroll=y,document[this.exitK]()},t.isFullscreen=function(){return document[this.elementK]}),t}}}),((t,e)=>{"function"==typeof define&&define.amd?define(e):"object"==typeof exports?module.exports=e():t.PhotoSwipe=e()})(this,function(){return function(d,j,e,R){var p={features:null,bind:function(t,e,i,n){var o=(n?"remove":"add")+"EventListener";e=e.split(" ");for(var s=0;s<e.length;s++)e[s]&&t[o](e[s],i,!1)},isArray:function(t){return t instanceof Array},createEl:function(t,e){e=document.createElement(e||"div");return t&&(e.className=t),e},getScrollY:function(){var t=window.pageYOffset;return void 0!==t?t:document.documentElement.scrollTop},unbind:function(t,e,i){p.bind(t,e,i,!0)},removeClass:function(t,e){e=new RegExp("(\\s|^)"+e+"(\\s|$)");t.className=t.className.replace(e," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(t,e){p.hasClass(t,e)||(t.className+=(t.className?" ":"")+e)},hasClass:function(t,e){return t.className&&new RegExp("(^|\\s)"+e+"(\\s|$)").test(t.className)},getChildByClass:function(t,e){for(var i=t.firstChild;i;){if(p.hasClass(i,e))return i;i=i.nextSibling}},arraySearch:function(t,e,i){for(var n=t.length;n--;)if(t[n][i]===e)return n;return-1},extend:function(t,e,i){for(var n in e)if(e.hasOwnProperty(n)){if(i&&t.hasOwnProperty(n))continue;t[n]=e[n]}},easing:{sine:{out:function(t){return Math.sin(t*(Math.PI/2))},inOut:function(t){return-(Math.cos(Math.PI*t)-1)/2}},cubic:{out:function(t){return--t*t*t+1}}},detectFeatures:function(){if(p.features)return p.features;var t,e,i=p.createEl().style,n="",o={};o.oldIE=document.all&&!document.addEventListener,o.touch="ontouchstart"in window,window.requestAnimationFrame&&(o.raf=window.requestAnimationFrame,o.caf=window.cancelAnimationFrame),o.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,o.pointerEvent||(t=navigator.userAgent,/iP(hone|od)/.test(navigator.platform)&&(e=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/))&&0<e.length&&1<=(e=parseInt(e[1],10))&&e<8&&(o.isOldIOSPhone=!0),e=(e=t.match(/Android\s([0-9\.]*)/))?e[1]:0,1<=(e=parseFloat(e))&&(e<4.4&&(o.isOldAndroid=!0),o.androidVersion=e),o.isMobileOpera=/opera mini|opera mobi/i.test(t));for(var s,r,a,l=["transform","perspective","animationName"],c=["","webkit","Moz","ms","O"],h=0;h<4;h++){for(var n=c[h],u=0;u<3;u++)s=l[u],r=n+(n?s.charAt(0).toUpperCase()+s.slice(1):s),!o[s]&&r in i&&(o[s]=r);n&&!o.raf&&(n=n.toLowerCase(),o.raf=window[n+"RequestAnimationFrame"],o.raf)&&(o.caf=window[n+"CancelAnimationFrame"]||window[n+"CancelRequestAnimationFrame"])}return o.raf||(a=0,o.raf=function(t){var e=(new Date).getTime(),i=Math.max(0,16-(e-a)),n=window.setTimeout(function(){t(e+i)},i);return a=e+i,n},o.caf=function(t){clearTimeout(t)}),o.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,p.features=o}},m=(p.detectFeatures(),p.features.oldIE&&(p.bind=function(t,e,i,n){e=e.split(" ");for(var o,s=(n?"detach":"attach")+"Event",r=function(){i.handleEvent.call(i)},a=0;a<e.length;a++)if(o=e[a])if("object"==typeof i&&i.handleEvent){if(n){if(!i["oldIE"+o])return!1}else i["oldIE"+o]=r;t[s]("on"+o,i["oldIE"+o])}else t[s]("on"+o,i)}),this),F=25,g={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(t){return"A"===t.tagName},getDoubleTapZoom:function(t,e){return t||e.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};p.extend(g,R);function t(){return{x:0,y:0}}function B(t,e){p.extend(m,e.publicMethods),qt.push(t)}function H(t){var e=M();return e-1<t?t-e:t<0?e+t:t}function s(t,e){return Kt[t]||(Kt[t]=[]),Kt[t].push(e)}function Z(t,e,i,n){n===m.currItem.initialZoomLevel?i[t]=m.currItem.initialPosition[t]:(i[t]=Jt(t,n),i[t]>e.min[t]?i[t]=e.min[t]:i[t]<e.max[t]&&(i[t]=e.max[t]))}function W(t){var e="";g.escKey&&27===t.keyCode?e="close":g.arrowKeys&&(37===t.keyCode?e="prev":39===t.keyCode&&(e="next")),!e||t.ctrlKey||t.altKey||t.shiftKey||t.metaKey||(t.preventDefault?t.preventDefault():t.returnValue=!1,m[e]())}function N(t){t&&(kt||At||v||It)&&(t.preventDefault(),t.stopPropagation())}function q(){m.setScrollOffset(0,p.getScrollY())}function V(t){var e;"mousedown"===t.type&&0<t.button||(Je?t.preventDefault():Tt&&"mousedown"===t.type||($e(t,!0)&&t.preventDefault(),C("pointerDown"),pt&&((e=p.arraySearch(ge,t.pointerId,"id"))<0&&(e=ge.length),ge[e]={x:t.pageX,y:t.pageY,id:t.pointerId}),t=(e=Fe(t)).length,c=null,ce(),l&&1!==t||(l=Ot=!0,p.bind(window,tt,m),Et=Rt=Pt=It=$t=kt=St=At=!1,Lt=null,C("firstTouchStart",e),S(Ht,w),Bt.x=Bt.y=0,S($,e[0]),S(me,$),fe.x=_.x*Zt,ye=[{x:$.x,y:$.y}],bt=_t=E(),ne(y,!0),Te(),Se()),!h&&1<t&&!v&&!$t&&(it=y,h=St=!(At=!1),Bt.y=Bt.x=0,S(Ht,w),S(k,e[0]),S(pe,e[1]),Me(k,pe,Ce),be.x=Math.abs(Ce.x)-w.x,be.y=Math.abs(Ce.y)-w.y,zt=Ie(k,pe))))}function U(t){var e;t.preventDefault(),pt&&-1<(e=p.arraySearch(ge,t.pointerId,"id"))&&((e=ge[e]).x=t.pageX,e.y=t.pageY),l&&(e=Fe(t),Lt||kt||h?c=e:z.x!==_.x*Zt?Lt="h":(t=Math.abs(e[0].x-$.x)-Math.abs(e[0].y-$.y),Math.abs(t)>=de&&(Lt=0<t?"h":"v",c=e)))}function K(t){if(a.isOldAndroid){if(Tt&&"mouseup"===t.type)return;-1<t.type.indexOf("touch")&&(clearTimeout(Tt),Tt=setTimeout(function(){Tt=0},600))}var e;C("pointerUp"),$e(t,!1)&&t.preventDefault(),pt&&-1<(s=p.arraySearch(ge,t.pointerId,"id"))&&(e=ge.splice(s,1)[0],navigator.msPointerEnabled&&(e.type={4:"mouse",2:"touch",3:"pen"}[t.pointerType],e.type)||(e.type=t.pointerType||"mouse"));var i=(s=Fe(t)).length;if(2===(i="mouseup"===t.type?0:i))return!(c=null);1===i&&S(me,s[0]),0!==i||Lt||v||(e||("mouseup"===t.type?e={x:t.pageX,y:t.pageY,type:"mouse"}:t.changedTouches&&t.changedTouches[0]&&(e={x:t.changedTouches[0].pageX,y:t.changedTouches[0].pageY,type:"touch"})),C("touchRelease",t,e));var n,o,s=-1;if(0===i&&(l=!1,p.unbind(window,tt,m),Te(),h?s=0:-1!==_e&&(s=E()-_e)),_e=1===i?E():-1,t=-1!==s&&s<150?"zoom":"swipe",h&&i<2&&(h=!1,1===i&&(t="zoomPointerUp"),C("zoomGestureEnded")),c=null,kt||At||v||It)if(ce(),(Ct=Ct||Ze()).calculateSwipeSpeed("x"),It)Oe()<g.verticalDragRange?m.close():(n=w.y,o=jt,he("verticalDrag",0,1,300,p.easing.cubic.out,function(t){w.y=(m.currItem.initialPosition.y-n)*t+n,I((1-o)*t+o),T()}),C("onVerticalDrag",1));else{if(($t||v)&&0===i){if(Ne(t,Ct))return;t="zoomPointerUp"}if(!v)return"swipe"!==t?void Ve():void(!$t&&y>m.currItem.fitRatio&&We(Ct))}}var Q,Y,G,f,X,J,tt,et,n,y,it,nt,ot,st,rt,r,at,lt,ct,ht,ut,dt,pt,o,mt,gt,ft,yt,vt,wt,a,xt,_t,bt,Ct,Et,It,Tt,l,St,At,kt,Dt,$t,c,h,zt,u,Mt,v,Lt,Ot,Pt,jt,Rt,Ft,Bt=t(),Ht=t(),w=t(),x={},Zt=0,Wt={},_=t(),b=0,Nt=!0,qt=[],Vt={},Ut=!1,Kt={},C=function(t){var e=Kt[t];if(e){var i=Array.prototype.slice.call(arguments);i.shift();for(var n=0;n<e.length;n++)e[n].apply(m,i)}},E=function(){return(new Date).getTime()},I=function(t){jt=t,m.bg.style.opacity=t*g.bgOpacity},Qt=function(t,e,i,n,o){(!Ut||o&&o!==m.currItem)&&(n/=(o||m.currItem).fitRatio),t[dt]=nt+e+"px, "+i+"px"+ot+" scale("+n+")"},T=function(t){Mt&&(t&&(y>m.currItem.fitRatio?Ut||(li(m.currItem,!1,!0),Ut=!0):Ut&&(li(m.currItem),Ut=!1)),Qt(Mt,w.x,w.y,y))},Yt=function(t){t.container&&Qt(t.container.style,t.initialPosition.x,t.initialPosition.y,t.initialZoomLevel,t)},Gt=function(t,e){e[dt]=nt+t+"px, 0px"+ot},Xt=function(t,e){var i;!g.loop&&e&&(e=f+(_.x*Zt-t)/_.x,i=Math.round(t-z.x),e<0&&0<i||e>=M()-1&&i<0)&&(t=z.x+i*g.mainScrollEndFriction),z.x=t,Gt(t,X)},Jt=function(t,e){var i=be[t]-Wt[t];return Ht[t]+Bt[t]+i-e/it*i},S=function(t,e){t.x=e.x,t.y=e.y,e.id&&(t.id=e.id)},te=function(t){t.x=Math.round(t.x),t.y=Math.round(t.y)},ee=null,ie=function(){ee&&(p.unbind(document,"mousemove",ie),p.addClass(d,"pswp--has_mouse"),g.mouseUsed=!0,C("mouseUsed")),ee=setTimeout(function(){ee=null},100)},ne=function(t,e){t=ri(m.currItem,x,t);return e&&(u=t),t},oe=function(t){return(t=t||m.currItem).initialZoomLevel},se=function(t){return 0<(t=t||m.currItem).w?g.maxSpreadZoom:1},A={},re=0,ae=function(t){A[t]&&(A[t].raf&&gt(A[t].raf),re--,delete A[t])},le=function(t){A[t]&&ae(t),A[t]||(re++,A[t]={})},ce=function(){for(var t in A)A.hasOwnProperty(t)&&ae(t)},he=function(t,e,i,n,o,s,r){function a(){A[t]&&(l=E()-c,n<=l?(ae(t),s(i),r&&r()):(s((i-e)*o(l/n)+e),A[t].raf=mt(a)))}var l,c=E();le(t);a()},R={shout:C,listen:s,viewportSize:x,options:g,isMainScrollAnimating:function(){return v},getZoomLevel:function(){return y},getCurrentIndex:function(){return f},isDragging:function(){return l},isZooming:function(){return h},setScrollOffset:function(t,e){Wt.x=t,wt=Wt.y=e,C("updateScrollOffset",Wt)},applyZoomPan:function(t,e,i,n){w.x=e,w.y=i,y=t,T(n)},init:function(){if(!Q&&!Y){m.framework=p,m.template=d,m.bg=p.getChildByClass(d,"pswp__bg"),ft=d.className,Q=!0,a=p.detectFeatures(),mt=a.raf,gt=a.caf,dt=a.transform,vt=a.oldIE,m.scrollWrap=p.getChildByClass(d,"pswp__scroll-wrap"),m.container=p.getChildByClass(m.scrollWrap,"pswp__container"),X=m.container.style,m.itemHolders=r=[{el:m.container.children[0],wrap:0,index:-1},{el:m.container.children[1],wrap:0,index:-1},{el:m.container.children[2],wrap:0,index:-1}],r[0].el.style.display=r[2].el.style.display="none",dt?(e=a.perspective&&!o,nt="translate"+(e?"3d(":"("),ot=a.perspective?", 0px)":")"):(dt="left",p.addClass(d,"pswp--ie"),Gt=function(t,e){e.left=t+"px"},Yt=function(t){var e=1<t.fitRatio?1:t.fitRatio,i=t.container.style,n=e*t.h;i.width=e*t.w+"px",i.height=n+"px",i.left=t.initialPosition.x+"px",i.top=t.initialPosition.y+"px"},T=function(){var t,e,i,n;Mt&&(t=Mt,n=(i=1<(e=m.currItem).fitRatio?1:e.fitRatio)*e.h,t.width=i*e.w+"px",t.height=n+"px",t.left=w.x+"px",t.top=w.y+"px")}),n={resize:m.updateSize,orientationchange:function(){clearTimeout(xt),xt=setTimeout(function(){x.x!==m.scrollWrap.clientWidth&&m.updateSize()},500)},scroll:q,keydown:W,click:N};var t,e=a.isOldIOSPhone||a.isOldAndroid||a.isMobileOpera;for(a.animationName&&a.transform&&!e||(g.showAnimationDuration=g.hideAnimationDuration=0),t=0;t<qt.length;t++)m["init"+qt[t]]();j&&(m.ui=new j(m,p)).init(),C("firstUpdate"),f=f||g.index||0,(isNaN(f)||f<0||f>=M())&&(f=0),m.currItem=ti(f),(a.isOldIOSPhone||a.isOldAndroid)&&(Nt=!1),d.setAttribute("aria-hidden","false"),g.modal&&(Nt?d.style.position="fixed":(d.style.position="absolute",d.style.top=p.getScrollY()+"px")),void 0===wt&&(C("initialLayout"),wt=yt=p.getScrollY());var i="pswp--open ";for(g.mainClass&&(i+=g.mainClass+" "),g.showHideOpacity&&(i+="pswp--animate_opacity "),i=(i=(i+=o?"pswp--touch":"pswp--notouch")+(a.animationName?" pswp--css_animation":""))+(a.svg?" pswp--svg":""),p.addClass(d,i),m.updateSize(),J=-1,b=null,t=0;t<3;t++)Gt((t+J)*_.x,r[t].el.style);vt||p.bind(m.scrollWrap,et,m),s("initialZoomInEnd",function(){m.setContent(r[0],f-1),m.setContent(r[2],f+1),r[0].el.style.display=r[2].el.style.display="block",g.focus&&d.focus(),p.bind(document,"keydown",m),a.transform&&p.bind(m.scrollWrap,"click",m),g.mouseUsed||p.bind(document,"mousemove",ie),p.bind(window,"resize scroll orientationchange",m),C("bindEvents")}),m.setContent(r[1],f),m.updateCurrItem(),C("afterInit"),Nt||(st=setInterval(function(){re||l||h||y!==m.currItem.initialZoomLevel||m.updateSize()},1e3)),p.addClass(d,"pswp--visible")}var e},close:function(){Q&&(Y=!(Q=!1),C("close"),p.unbind(window,"resize scroll orientationchange",m),p.unbind(window,"scroll",n.scroll),p.unbind(document,"keydown",m),p.unbind(document,"mousemove",ie),a.transform&&p.unbind(m.scrollWrap,"click",m),l&&p.unbind(window,tt,m),clearTimeout(xt),C("unbindEvents"),ei(m.currItem,null,!0,m.destroy))},destroy:function(){C("destroy"),Ye&&clearTimeout(Ye),d.setAttribute("aria-hidden","true"),d.className=ft,st&&clearInterval(st),p.unbind(m.scrollWrap,et,m),p.unbind(window,"scroll",m),Te(),ce(),Kt=null},panTo:function(t,e,i){i||(t>u.min.x?t=u.min.x:t<u.max.x&&(t=u.max.x),e>u.min.y?e=u.min.y:e<u.max.y&&(e=u.max.y)),w.x=t,w.y=e,T()},handleEvent:function(t){t=t||window.event,n[t.type]&&n[t.type](t)},goTo:function(t){var e=(t=H(t))-f;b=e,f=t,m.currItem=ti(f),Zt-=e,Xt(_.x*Zt),ce(),v=!1,m.updateCurrItem()},next:function(){m.goTo(f+1)},prev:function(){m.goTo(f-1)},updateCurrZoomItem:function(t){var e;t&&C("beforeChange",0),Mt=r[1].el.children.length&&(e=r[1].el.children[0],p.hasClass(e,"pswp__zoom-wrap"))?e.style:null,u=m.currItem.bounds,it=y=m.currItem.initialZoomLevel,w.x=u.center.x,w.y=u.center.y,t&&C("afterChange")},invalidateCurrItems:function(){rt=!0;for(var t=0;t<3;t++)r[t].item&&(r[t].item.needsUpdate=!0)},updateCurrItem:function(t){if(0!==b){var e,i=Math.abs(b);if(!(t&&i<2)){m.currItem=ti(f),Ut=!1,C("beforeChange",b),3<=i&&(J+=b+(0<b?-3:3),i=3);for(var n=0;n<i;n++)0<b?(e=r.shift(),r[2]=e,Gt((++J+2)*_.x,e.el.style),m.setContent(e,f-i+n+1+1)):(e=r.pop(),r.unshift(e),Gt(--J*_.x,e.el.style),m.setContent(e,f+i-n-1-1));Mt&&1===Math.abs(b)&&(t=ti(at)).initialZoomLevel!==y&&(ri(t,x),li(t),Yt(t)),b=0,m.updateCurrZoomItem(),at=f,C("afterChange")}}},updateSize:function(t){if(!Nt&&g.modal){var e=p.getScrollY();if(wt!==e&&(d.style.top=e+"px",wt=e),!t&&Vt.x===window.innerWidth&&Vt.y===window.innerHeight)return;Vt.x=window.innerWidth,Vt.y=window.innerHeight,d.style.height=Vt.y+"px"}if(x.x=m.scrollWrap.clientWidth,x.y=m.scrollWrap.clientHeight,q(),_.x=x.x+Math.round(x.x*g.spacing),_.y=x.y,Xt(_.x*Zt),C("beforeResize"),void 0!==J){for(var i,n,o,s=0;s<3;s++)i=r[s],Gt((s+J)*_.x,i.el.style),o=f+s-1,g.loop&&2<M()&&(o=H(o)),(n=ti(o))&&(rt||n.needsUpdate||!n.bounds)?(m.cleanSlide(n),m.setContent(i,o),1===s&&(m.currItem=n,m.updateCurrZoomItem(!0)),n.needsUpdate=!1):-1===i.index&&0<=o&&m.setContent(i,o),n&&n.container&&(ri(n,x),li(n),Yt(n));rt=!1}it=y=m.currItem.initialZoomLevel,(u=m.currItem.bounds)&&(w.x=u.center.x,w.y=u.center.y,T(!0)),C("resize")},zoomTo:function(e,t,i,n,o){t&&(it=y,be.x=Math.abs(t.x)-w.x,be.y=Math.abs(t.y)-w.y,S(Ht,w));function s(t){1===t?(y=e,w.x=r.x,w.y=r.y):(y=(e-a)*t+a,w.x=(r.x-l.x)*t+l.x,w.y=(r.y-l.y)*t+l.y),o&&o(t),T(1===t)}var t=ne(e,!1),r={},a=(Z("x",t,r,e),Z("y",t,r,e),y),l={x:w.x,y:w.y};te(r);i?he("customZoomTo",0,1,i,n||p.easing.sine.inOut,s):s(1)}},ue=30,de=10,k={},pe={},D={},$={},me={},ge=[],fe={},ye=[],ve={},we=0,xe=t(),_e=0,z=t(),be=t(),Ce=t(),Ee=function(t,e){return t.x===e.x&&t.y===e.y},Ie=function(t,e){return ve.x=Math.abs(t.x-e.x),ve.y=Math.abs(t.y-e.y),Math.sqrt(ve.x*ve.x+ve.y*ve.y)},Te=function(){Dt&&(gt(Dt),Dt=null)},Se=function(){l&&(Dt=mt(Se),He())},Ae=function(){return!("fit"===g.scaleMode&&y===m.currItem.initialZoomLevel)},ke=function(t,e){return!(!t||t===document)&&!(t.getAttribute("class")&&-1<t.getAttribute("class").indexOf("pswp__scroll-wrap"))&&(e(t)?t:ke(t.parentNode,e))},De={},$e=function(t,e){return De.prevent=!ke(t.target,g.isClickableElement),C("preventDragEvent",t,e,De),De.prevent},ze=function(t,e){return e.x=t.pageX,e.y=t.pageY,e.id=t.identifier,e},Me=function(t,e,i){i.x=.5*(t.x+e.x),i.y=.5*(t.y+e.y)},Le=function(t,e,i){var n;50<t-bt&&((n=2<ye.length?ye.shift():{}).x=e,n.y=i,ye.push(n),bt=t)},Oe=function(){var t=w.y-m.currItem.initialPosition.y;return 1-Math.abs(t/(x.y/2))},Pe={},je={},Re=[],Fe=function(t){for(;0<Re.length;)Re.pop();return pt?(Ft=0,ge.forEach(function(t){0===Ft?Re[0]=t:1===Ft&&(Re[1]=t),Ft++})):-1<t.type.indexOf("touch")?t.touches&&0<t.touches.length&&(Re[0]=ze(t.touches[0],Pe),1<t.touches.length)&&(Re[1]=ze(t.touches[1],je)):(Pe.x=t.pageX,Pe.y=t.pageY,Pe.id="",Re[0]=Pe),Re},Be=function(t,e){var i,n,o,s=w[t]+e[t],r=z.x+e.x,a=z.x-fe.x,l=s>u.min[t]||s<u.max[t]?g.panEndFriction:1,s=w[t]+e[t]*l;return!g.allowPanToNext&&y!==m.currItem.initialZoomLevel||(Mt?"h"!==Lt||"x"!==t||At||(0<e[t]?(s>u.min[t]&&(l=g.panEndFriction,u.min[t],i=u.min[t]-Ht[t]),(i<=0||a<0)&&1<M()?(o=r,a<0&&r>fe.x&&(o=fe.x)):u.min.x!==u.max.x&&(n=s)):(s<u.max[t]&&(l=g.panEndFriction,u.max[t],i=Ht[t]-u.max[t]),(i<=0||0<a)&&1<M()?(o=r,0<a&&r<fe.x&&(o=fe.x)):u.min.x!==u.max.x&&(n=s))):o=r,"x"!==t)?void(v||$t||y>m.currItem.fitRatio&&(w[t]+=e[t]*l)):(void 0!==o&&(Xt(o,!0),$t=o!==fe.x),u.min.x!==u.max.x&&(void 0!==n?w.x=n:$t||(w.x+=e.x*l)),void 0!==o)},He=function(){var t,e,i,n,o,s;c&&0!==(t=c.length)&&(S(k,c[0]),D.x=k.x-$.x,D.y=k.y-$.y,h&&1<t?($.x=k.x,$.y=k.y,!D.x&&!D.y&&Ee(c[1],pe)||(S(pe,c[1]),At||(At=!0,C("zoomGestureStarted")),t=Ie(k,pe),(e=qe(t))>m.currItem.initialZoomLevel+m.currItem.initialZoomLevel/15&&(Rt=!0),i=1,n=oe(),o=se(),e<n?g.pinchToClose&&!Rt&&it<=m.currItem.initialZoomLevel?(I(s=1-(n-e)/(n/1.2)),C("onPinchClose",s),Pt=!0):e=n-(i=1<(i=(n-e)/n)?1:i)*(n/3):o<e&&(e=o+(i=1<(i=(e-o)/(6*n))?1:i)*n),i<0&&(i=0),Me(k,pe,xe),Bt.x+=xe.x-Ce.x,Bt.y+=xe.y-Ce.y,S(Ce,xe),w.x=Jt("x",e),w.y=Jt("y",e),Et=y<e,y=e,T())):Lt&&(Ot&&(Ot=!1,Math.abs(D.x)>=de&&(D.x-=c[0].x-me.x),Math.abs(D.y)>=de)&&(D.y-=c[0].y-me.y),$.x=k.x,$.y=k.y,0===D.x&&0===D.y||("v"===Lt&&g.closeOnVerticalDrag&&!Ae()?(Bt.y+=D.y,w.y+=D.y,s=Oe(),It=!0,C("onVerticalDrag",s),I(s),T()):(Le(E(),k.x,k.y),kt=!0,u=m.currItem.bounds,Be("x",D)||(Be("y",D),te(w),T())))))},Ze=function(){var e,i,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(t){i=(1<ye.length?(e=E()-bt+50,ye[ye.length-2]):(e=E()-_t,me))[t],n.lastFlickOffset[t]=$[t]-i,n.lastFlickDist[t]=Math.abs(n.lastFlickOffset[t]),20<n.lastFlickDist[t]?n.lastFlickSpeed[t]=n.lastFlickOffset[t]/e:n.lastFlickSpeed[t]=0,Math.abs(n.lastFlickSpeed[t])<.1&&(n.lastFlickSpeed[t]=0),n.slowDownRatio[t]=.95,n.slowDownRatioReverse[t]=1-n.slowDownRatio[t],n.speedDecelerationRatio[t]=1},calculateOverBoundsAnimOffset:function(e,t){n.backAnimStarted[e]||(w[e]>u.min[e]?n.backAnimDestination[e]=u.min[e]:w[e]<u.max[e]&&(n.backAnimDestination[e]=u.max[e]),void 0!==n.backAnimDestination[e]&&(n.slowDownRatio[e]=.7,n.slowDownRatioReverse[e]=1-n.slowDownRatio[e],n.speedDecelerationRatioAbs[e]<.05)&&(n.lastFlickSpeed[e]=0,n.backAnimStarted[e]=!0,he("bounceZoomPan"+e,w[e],n.backAnimDestination[e],t||300,p.easing.sine.out,function(t){w[e]=t,T()})))},calculateAnimOffset:function(t){n.backAnimStarted[t]||(n.speedDecelerationRatio[t]=n.speedDecelerationRatio[t]*(n.slowDownRatio[t]+n.slowDownRatioReverse[t]-n.slowDownRatioReverse[t]*n.timeDiff/10),n.speedDecelerationRatioAbs[t]=Math.abs(n.lastFlickSpeed[t]*n.speedDecelerationRatio[t]),n.distanceOffset[t]=n.lastFlickSpeed[t]*n.speedDecelerationRatio[t]*n.timeDiff,w[t]+=n.distanceOffset[t])},panAnimLoop:function(){A.zoomPan&&(A.zoomPan.raf=mt(n.panAnimLoop),n.now=E(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),T(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05)&&n.speedDecelerationRatioAbs.y<.05&&(w.x=Math.round(w.x),w.y=Math.round(w.y),T(),ae("zoomPan"))}};return n},We=function(t){return t.calculateSwipeSpeed("y"),u=m.currItem.bounds,t.backAnimDestination={},t.backAnimStarted={},Math.abs(t.lastFlickSpeed.x)<=.05&&Math.abs(t.lastFlickSpeed.y)<=.05?(t.speedDecelerationRatioAbs.x=t.speedDecelerationRatioAbs.y=0,t.calculateOverBoundsAnimOffset("x"),t.calculateOverBoundsAnimOffset("y"),!0):(le("zoomPan"),t.lastNow=E(),void t.panAnimLoop())},Ne=function(t,e){var i,n,o;v||(we=f),"swipe"===t&&(t=$.x-me.x,s=e.lastFlickDist.x<10,ue<t&&(s||20<e.lastFlickOffset.x)?n=-1:t<-ue&&(s||e.lastFlickOffset.x<-20)&&(n=1)),n&&((f+=n)<0?(f=g.loop?M()-1:0,o=!0):f>=M()&&(f=g.loop?0:M()-1,o=!0),o&&!g.loop||(b+=n,Zt-=n,i=!0));var t=_.x*Zt,s=Math.abs(t-z.x),r=i||t>z.x==0<e.lastFlickSpeed.x?(r=0<Math.abs(e.lastFlickSpeed.x)?s/Math.abs(e.lastFlickSpeed.x):333,r=Math.min(r,400),Math.max(r,250)):333;return we===f&&(i=!1),v=!0,C("mainScrollAnimStart"),he("mainScroll",z.x,t,r,p.easing.cubic.out,Xt,function(){ce(),v=!1,we=-1,!i&&we===f||m.updateCurrItem(),C("mainScrollAnimComplete")}),i&&m.updateCurrItem(!0),i},qe=function(t){return 1/zt*t*it},Ve=function(){var t=y,e=oe(),i=se();y<e?t=e:i<y&&(t=i);var n,o=jt;return Pt&&!Et&&!Rt&&y<e?m.close():(Pt&&(n=function(t){I((1-o)*t+o)}),m.zoomTo(t,0,200,p.easing.cubic.out,n)),!0};B("Gestures",{publicMethods:{initGestures:function(){function t(t,e,i,n,o){lt=t+e,ct=t+i,ht=t+n,ut=o?t+o:""}(pt=a.pointerEvent)&&a.touch&&(a.touch=!1),pt?navigator.msPointerEnabled?t("MSPointer","Down","Move","Up","Cancel"):t("pointer","down","move","up","cancel"):a.touch?(t("touch","start","move","end","cancel"),o=!0):t("mouse","down","move","up"),tt=ct+" "+ht+" "+ut,et=lt,pt&&!o&&(o=1<navigator.maxTouchPoints||1<navigator.msMaxTouchPoints),m.likelyTouchDevice=o,n[lt]=V,n[ct]=U,n[ht]=K,ut&&(n[ut]=n[ht]),a.touch&&(et+=" mousedown",tt+=" mousemove mouseup",n.mousedown=n[lt],n.mousemove=n[ct],n.mouseup=n[ht]),o||(g.allowPanToNext=!1)}}});function Ue(t){function e(){t.loading=!1,t.loaded=!0,t.loadComplete?t.loadComplete(t):t.img=null,i.onload=i.onerror=null,i=null}t.loading=!0,t.loaded=!1;var i=t.img=p.createEl("pswp__img","img");i.onload=e,i.onerror=function(){t.loadError=!0,e()},i.src=t.src}function Ke(t,e){return t.src&&t.loadError&&t.container&&(e&&(t.container.innerHTML=""),t.container.innerHTML=g.errorMsg.replace("%url%",t.src),1)}function Qe(){if(ii.length){for(var t,e=0;e<ii.length;e++)(t=ii[e]).holder.index===t.index&&ai(t.index,t.item,t.baseDiv,t.img,!1,t.clearPlaceholder);ii=[]}}var Ye,Ge,Xe,Je,ti,M,ei=function(s,t,r,e){function a(){ae("initialZoom"),r?(m.template.removeAttribute("style"),m.bg.removeAttribute("style")):(I(1),t&&(t.style.display="block"),p.addClass(d,"pswp--animated-in"),C("initialZoom"+(r?"OutEnd":"InEnd"))),e&&e(),Je=!1}Ye&&clearTimeout(Ye),Xe=Je=!0,s.initialLayout?(l=s.initialLayout,s.initialLayout=null):l=g.getThumbBoundsFn&&g.getThumbBoundsFn(f);var l,c,h,u=r?g.hideAnimationDuration:g.showAnimationDuration;u&&l&&void 0!==l.x?(c=G,h=!m.currItem.src||m.currItem.loadError||g.showHideOpacity,s.miniImg&&(s.miniImg.style.webkitBackfaceVisibility="hidden"),r||(y=l.w/s.w,w.x=l.x,w.y=l.y-yt,m[h?"template":"bg"].style.opacity=.001,T()),le("initialZoom"),r&&!c&&p.removeClass(d,"pswp--animated-in"),h&&(r?p[(c?"remove":"add")+"Class"](d,"pswp--animate_opacity"):setTimeout(function(){p.addClass(d,"pswp--animate_opacity")},30)),Ye=setTimeout(function(){var e,i,n,o,t;C("initialZoom"+(r?"Out":"In")),r?(e=l.w/s.w,i={x:w.x,y:w.y},n=y,o=jt,t=function(t){1===t?(y=e,w.x=l.x,w.y=l.y-wt):(y=(e-n)*t+n,w.x=(l.x-i.x)*t+i.x,w.y=(l.y-wt-i.y)*t+i.y),T(),h?d.style.opacity=1-t:I(o-t*o)},c?he("initialZoom",0,1,u,p.easing.cubic.out,t,a):(t(1),Ye=setTimeout(a,u+20))):(y=s.initialZoomLevel,S(w,s.initialPosition),T(),I(1),h?d.style.opacity=1:I(1),Ye=setTimeout(a,u+20))},r?25:90)):(C("initialZoom"+(r?"Out":"In")),y=s.initialZoomLevel,S(w,s.initialPosition),T(),d.style.opacity=r?0:1,I(1),u?setTimeout(function(){a()},u):a())},L={},ii=[],ni={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return Ge.length}},oi=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},si=function(t,e,i){var n=t.bounds;n.center.x=Math.round((L.x-e)/2),n.center.y=Math.round((L.y-i)/2)+t.vGap.top,n.max.x=e>L.x?Math.round(L.x-e):n.center.x,n.max.y=i>L.y?Math.round(L.y-i)+t.vGap.top:n.center.y,n.min.x=e>L.x?0:n.center.x,n.min.y=i>L.y?t.vGap.top:n.center.y},ri=function(t,e,i){var n,o;return t.src&&!t.loadError?((n=!i)&&(t.vGap||(t.vGap={top:0,bottom:0}),C("parseVerticalMargin",t)),L.x=e.x,L.y=e.y-t.vGap.top-t.vGap.bottom,n&&(e=L.x/t.w,o=L.y/t.h,t.fitRatio=e<o?e:o,"orig"===(e=g.scaleMode)?i=1:"fit"===e&&(i=t.fitRatio),t.initialZoomLevel=i=1<i?1:i,t.bounds||(t.bounds=oi())),i?(si(t,t.w*i,t.h*i),n&&i===t.initialZoomLevel&&(t.initialPosition=t.bounds.center),t.bounds):void 0):(t.w=t.h=0,t.initialZoomLevel=t.fitRatio=1,t.bounds=oi(),t.initialPosition=t.bounds.center,t.bounds)},ai=function(t,e,i,n,o,s){e.loadError||n&&(e.imageAppended=!0,li(e,n,e===m.currItem&&Ut),i.appendChild(n),s)&&setTimeout(function(){e&&e.loaded&&e.placeholder&&(e.placeholder.style.display="none",e.placeholder=null)},500)},li=function(t,e,i){var n;t.src&&(e=e||t.container.lastChild,n=i?t.w:Math.round(t.w*t.fitRatio),i=i?t.h:Math.round(t.h*t.fitRatio),t.placeholder&&!t.loaded&&(t.placeholder.style.width=n+"px",t.placeholder.style.height=i+"px"),e.style.width=n+"px",e.style.height=i+"px")};B("Controller",{publicMethods:{lazyLoadItem:function(t){t=H(t);var e=ti(t);e&&(!e.loaded&&!e.loading||rt)&&(C("gettingData",t,e),e.src)&&Ue(e)},initController:function(){p.extend(g,ni,!0),m.items=Ge=e,ti=m.getItemAt,M=g.getNumItemsFn,g.loop,M()<3&&(g.loop=!1),s("beforeChange",function(t){for(var e=g.preload,i=null===t||0<=t,n=Math.min(e[0],M()),o=Math.min(e[1],M()),s=1;s<=(i?o:n);s++)m.lazyLoadItem(f+s);for(s=1;s<=(i?n:o);s++)m.lazyLoadItem(f-s)}),s("initialLayout",function(){m.currItem.initialLayout=g.getThumbBoundsFn&&g.getThumbBoundsFn(f)}),s("mainScrollAnimComplete",Qe),s("initialZoomInEnd",Qe),s("destroy",function(){for(var t,e=0;e<Ge.length;e++)(t=Ge[e]).container&&(t.container=null),t.placeholder&&(t.placeholder=null),t.img&&(t.img=null),t.preloader&&(t.preloader=null),t.loadError&&(t.loaded=t.loadError=!1);ii=null})},getItemAt:function(t){return 0<=t&&void 0!==Ge[t]&&Ge[t]},allowProgressiveImg:function(){return g.forceProgressiveLoading||!o||g.mouseUsed||1200<screen.width},setContent:function(e,i){g.loop&&(i=H(i));var t=m.getItemAt(e.index);t&&(t.container=null);var n,o,s,t=m.getItemAt(i);t?(C("gettingData",i,t),e.index=i,o=(e.item=t).container=p.createEl("pswp__zoom-wrap"),!t.src&&t.html&&(t.html.tagName?o.appendChild(t.html):o.innerHTML=t.html),Ke(t),ri(t,x),!t.src||t.loadError||t.loaded?t.src&&!t.loadError&&((n=p.createEl("pswp__img","img")).style.opacity=1,n.src=t.src,li(t,n),ai(i,t,o,n,!0)):(t.loadComplete=function(t){if(Q){if(e&&e.index===i){if(Ke(t,!0))return t.loadComplete=t.img=null,ri(t,x),Yt(t),void(e.index===f&&m.updateCurrZoomItem());t.imageAppended?!Je&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null):a.transform&&(v||Je)?ii.push({item:t,baseDiv:o,img:t.img,index:i,holder:e,clearPlaceholder:!0}):ai(i,t,o,t.img,v||Je,!0)}t.loadComplete=null,t.img=null,C("imageLoadComplete",i,t)}},p.features.transform&&(s="pswp__img pswp__img--placeholder",s+=t.msrc?"":" pswp__img--placeholder--blank",s=p.createEl(s,t.msrc?"img":""),t.msrc&&(s.src=t.msrc),li(t,s),o.appendChild(s),t.placeholder=s),t.loading||Ue(t),m.allowProgressiveImg()&&(!Xe&&a.transform?ii.push({item:t,baseDiv:o,img:t.img,index:i,holder:e}):ai(i,t,o,t.img,!0,!0))),Xe||i!==f?Yt(t):(Mt=o.style,ei(t,n||t.img)),e.el.innerHTML="",e.el.appendChild(o)):e.el.innerHTML=""},cleanSlide:function(t){t.img&&(t.img.onload=t.img.onerror=null),t.loaded=t.loading=t.img=t.imageAppended=!1}}});function ci(t,e,i){var n=document.createEvent("CustomEvent"),e={origEvent:t,target:t.target,releasePoint:e,pointerType:i||"touch"};n.initCustomEvent("pswpTap",!0,!0,e),t.target.dispatchEvent(n)}var hi,O,ui={};B("Tap",{publicMethods:{initTap:function(){s("firstTouchStart",m.onTapStart),s("touchRelease",m.onTapRelease),s("destroy",function(){ui={},hi=null})},onTapStart:function(t){1<t.length&&(clearTimeout(hi),hi=null)},onTapRelease:function(t,e){var i,n,o;!e||kt||St||re||(i=e,hi&&(clearTimeout(hi),hi=null,n=i,o=ui,Math.abs(n.x-o.x)<F)&&Math.abs(n.y-o.y)<F?C("doubleTap",i):"mouse"===e.type?ci(t,e,"mouse"):"BUTTON"===t.target.tagName.toUpperCase()||p.hasClass(t.target,"pswp__single-tap")?ci(t,e):(S(ui,i),hi=setTimeout(function(){ci(t,e),hi=null},300)))}}}),B("DesktopZoom",{publicMethods:{initDesktopZoom:function(){vt||(o?s("mouseUsed",function(){m.setupDesktopZoom()}):m.setupDesktopZoom(!0))},setupDesktopZoom:function(t){O={};var e="wheel mousewheel DOMMouseScroll";s("bindEvents",function(){p.bind(d,e,m.handleMouseWheel)}),s("unbindEvents",function(){O&&p.unbind(d,e,m.handleMouseWheel)}),m.mouseZoomedIn=!1;function i(){m.mouseZoomedIn&&(p.removeClass(d,"pswp--zoomed-in"),m.mouseZoomedIn=!1),y<1?p.addClass(d,"pswp--zoom-allowed"):p.removeClass(d,"pswp--zoom-allowed"),o()}var n,o=function(){n&&(p.removeClass(d,"pswp--dragging"),n=!1)};s("resize",i),s("afterChange",i),s("pointerDown",function(){m.mouseZoomedIn&&(n=!0,p.addClass(d,"pswp--dragging"))}),s("pointerUp",o),t||i()},handleMouseWheel:function(t){if(y<=m.currItem.fitRatio)return g.modal&&(!g.closeOnScroll||re||l?t.preventDefault():dt&&2<Math.abs(t.deltaY)&&(G=!0,m.close())),!0;if(t.stopPropagation(),O.x=0,"deltaX"in t)1===t.deltaMode?(O.x=18*t.deltaX,O.y=18*t.deltaY):(O.x=t.deltaX,O.y=t.deltaY);else if("wheelDelta"in t)t.wheelDeltaX&&(O.x=-.16*t.wheelDeltaX),O.y=t.wheelDeltaY?-.16*t.wheelDeltaY:-.16*t.wheelDelta;else{if(!("detail"in t))return;O.y=t.detail}ne(y,!0);var e=w.x-O.x,i=w.y-O.y;(g.modal||e<=u.min.x&&e>=u.max.x&&i<=u.min.y&&i>=u.max.y)&&t.preventDefault(),m.panTo(e,i)},toggleDesktopZoom:function(t){t=t||{x:x.x/2+Wt.x,y:x.y/2+Wt.y};var e=g.getDoubleTapZoom(!0,m.currItem),i=y===e;m.mouseZoomedIn=!i,m.zoomTo(i?m.currItem.initialZoomLevel:e,t,333),p[(i?"remove":"add")+"Class"](d,"pswp--zoomed-in")}}});function di(){mi&&clearTimeout(mi),fi&&clearTimeout(fi)}function pi(){var t=Ii(),e={};if(!(t.length<5)){var i,n=t.split("&");for(s=0;s<n.length;s++)!n[s]||(i=n[s].split("=")).length<2||(e[i[0]]=i[1]);if(g.galleryPIDs){for(var o=e.pid,s=e.pid=0;s<Ge.length;s++)if(Ge[s].pid===o){e.pid=s;break}}else e.pid=parseInt(e.pid,10)-1;e.pid<0&&(e.pid=0)}return e}var mi,gi,fi,yi,vi,wi,i,xi,_i,bi,P,Ci,Ei={history:!0,galleryUID:1},Ii=function(){return P.hash.substring(1)},Ti=function(){var t,e;fi&&clearTimeout(fi),re||l?fi=setTimeout(Ti,500):(yi?clearTimeout(gi):yi=!0,e=f+1,(t=ti(f)).hasOwnProperty("pid")&&(e=t.pid),t=i+"&gid="+g.galleryUID+"&pid="+e,xi||-1===P.hash.indexOf(t)&&(bi=!0),e=P.href.split("#")[0]+"#"+t,Ci?"#"+t!==window.location.hash&&history[xi?"replaceState":"pushState"]("",document.title,e):xi?P.replace(e):P.hash=t,xi=!0,gi=setTimeout(function(){yi=!1},60))};B("History",{publicMethods:{initHistory:function(){var t,e;p.extend(g,Ei,!0),g.history&&(P=window.location,xi=_i=bi=!1,i=Ii(),Ci="pushState"in history,-1<i.indexOf("gid=")&&(i=(i=i.split("&gid=")[0]).split("?gid=")[0]),s("afterChange",m.updateURL),s("unbindEvents",function(){p.unbind(window,"hashchange",m.onHashChange)}),t=function(){wi=!0,_i||(bi?history.back():i?P.hash=i:Ci?history.pushState("",document.title,P.pathname+P.search):P.hash=""),di()},s("unbindEvents",function(){G&&t()}),s("destroy",function(){wi||t()}),s("firstUpdate",function(){f=pi().pid}),-1<(e=i.indexOf("pid="))&&"&"===(i=i.substring(0,e)).slice(-1)&&(i=i.slice(0,-1)),setTimeout(function(){Q&&p.bind(window,"hashchange",m.onHashChange)},40))},onHashChange:function(){return Ii()===i?(_i=!0,void m.close()):void(yi||(vi=!0,m.goTo(pi().pid),vi=!1))},updateURL:function(){di(),vi||(xi?mi=setTimeout(Ti,800):Ti())}}}),p.extend(m,R)}}),((t,e)=>{"object"==typeof exports&&"undefined"!=typeof module?module.exports=e():"function"==typeof define&&define.amd?define(e):(t="undefined"!=typeof globalThis?globalThis:t||self).reframe=e()})(this,function(){function e(){for(var t=0,e=0,i=arguments.length;e<i;e++)t+=arguments[e].length;for(var n=Array(t),o=0,e=0;e<i;e++)for(var s=arguments[e],r=0,a=s.length;r<a;r++,o++)n[o]=s[r];return n}return function(t,o){return void 0===o&&(o="js-reframe"),("string"==typeof t?e(document.querySelectorAll(t)):"length"in t?e(t):[t]).forEach(function(t){var e,i,n;-1!==t.className.split(" ").indexOf(o)||-1<t.style.width.indexOf("%")||(e=t.getAttribute("height")||t.offsetHeight,i=t.getAttribute("width")||t.offsetWidth,e=("string"==typeof e?parseInt(e):e)/("string"==typeof i?parseInt(i):i)*100,(i=document.createElement("div")).className=o,(n=i.style).position="relative",n.width="100%",n.paddingTop=e+"%",(n=t.style).position="absolute",n.width="100%",n.height="100%",n.left="0",n.top="0",null!=(e=t.parentNode)&&e.insertBefore(i,t),null!=(n=t.parentNode)&&n.removeChild(t),i.appendChild(t))})}}),(()=>{var t=document.querySelector(".gh-burger");t&&t.addEventListener("click",function(){document.body.classList.contains("is-head-open")?document.body.classList.remove("is-head-open"):document.body.classList.add("is-head-open")})})(),lightbox(".kg-image-card > .kg-image[width][height], .kg-gallery-image > img"),reframe(document.querySelectorAll(['.gh-content iframe[src*="youtube.com"]','.gh-content iframe[src*="youtube-nocookie.com"]','.gh-content iframe[src*="player.vimeo.com"]','.gh-content iframe[src*="kickstarter.com"][src*="video.html"]',".gh-content object",".gh-content embed"].join(","))),dropdown(),(i=>{var n={};function o(t){var e;return(n[t]||(e=n[t]={i:t,l:!1,exports:{}},i[t].call(e.exports,e,e.exports,o),e.l=!0,e)).exports}o.m=i,o.c=n,o.d=function(t,e,i){o.o(t,e)||Object.defineProperty(t,e,{enumerable:!0,get:i})},o.r=function(t){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})},o.t=function(e,t){if(1&t&&(e=o(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var i=Object.create(null);if(o.r(i),Object.defineProperty(i,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var n in e)o.d(i,n,function(t){return e[t]}.bind(null,n));return i},o.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return o.d(e,"a",e),e},o.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},o.p="",o(o.s=10)})([,,function(t,e){t.exports=function(t){"complete"===document.readyState||"interactive"===document.readyState?t.call():document.attachEvent?document.attachEvent("onreadystatechange",function(){"interactive"===document.readyState&&t.call()}):document.addEventListener&&document.addEventListener("DOMContentLoaded",t)}},function(e,t,i){!function(t){t="undefined"!=typeof window?window:void 0!==t?t:"undefined"!=typeof self?self:{};e.exports=t}.call(this,i(4))},function(t,e){function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var n=function(){return this}();try{n=n||new Function("return this")()}catch(t){"object"===("undefined"==typeof window?"undefined":i(window))&&(n=window)}t.exports=n},,,,,,function(t,e,i){t.exports=i(11)},function(t,e,i){i.r(e);var e=i(2),e=i.n(e),o=i(3),s=i(12);function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var n,a=o.window.jarallax;o.window.jarallax=s.default,o.window.jarallax.noConflict=function(){return o.window.jarallax=a,this},void 0!==o.jQuery&&((i=function(){for(var t=arguments.length,e=new Array(t),i=0;i<t;i++)e[i]=arguments[i];Array.prototype.unshift.call(e,this);var n=s.default.apply(o.window,e);return"object"!==r(n)?n:this}).constructor=s.default.constructor,n=o.jQuery.fn.jarallax,o.jQuery.fn.jarallax=i,o.jQuery.fn.jarallax.noConflict=function(){return o.jQuery.fn.jarallax=n,this}),e()(function(){Object(s.default)(document.querySelectorAll("[data-jarallax]"))})},function(t,e,i){i.r(e);var n=i(2),n=i.n(n),p=i(3);function a(t,e){return(t=>{if(Array.isArray(t))return t})(t)||((t,e)=>{if("undefined"!=typeof Symbol&&Symbol.iterator in Object(t)){var i=[],n=!0,o=!1,s=void 0;try{for(var r,a=t[Symbol.iterator]();!(n=(r=a.next()).done)&&(i.push(r.value),!e||i.length!==e);n=!0);}catch(t){o=!0,s=t}finally{try{n||null==a.return||a.return()}finally{if(o)throw s}}return i}})(t,e)||((t,e)=>{var i;if(t)return"string"==typeof t?o(t,e):"Map"===(i="Object"===(i=Object.prototype.toString.call(t).slice(8,-1))&&t.constructor?t.constructor.name:i)||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?o(t,e):void 0})(t,e)||(()=>{throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")})()}function o(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,n=new Array(e);i<e;i++)n[i]=t[i];return n}function l(t){return(l="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function s(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}var r,m,c=p.window.navigator,h=-1<c.userAgent.indexOf("MSIE ")||-1<c.userAgent.indexOf("Trident/")||-1<c.userAgent.indexOf("Edge/"),u=/Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(c.userAgent),d=(()=>{for(var t="transform WebkitTransform MozTransform".split(" "),e=document.createElement("div"),i=0;i<t.length;i+=1)if(e&&void 0!==e.style[t[i]])return t[i];return!1})();function g(){m=u?(!r&&document.body&&((r=document.createElement("div")).style.cssText="position: fixed; top: -9999px; left: 0; height: 100vh; width: 0;",document.body.appendChild(r)),(r?r.clientHeight:0)||p.window.innerHeight||document.documentElement.clientHeight):p.window.innerHeight||document.documentElement.clientHeight}g(),p.window.addEventListener("resize",g),p.window.addEventListener("orientationchange",g),p.window.addEventListener("load",g),n()(function(){g()});var f=[];function y(){f.length&&(f.forEach(function(t,e){var i=t.instance,t=t.oldData,n=i.$item.getBoundingClientRect(),n={width:n.width,height:n.height,top:n.top,bottom:n.bottom,wndW:p.window.innerWidth,wndH:m},o=!t||t.wndW!==n.wndW||t.wndH!==n.wndH||t.width!==n.width||t.height!==n.height,t=o||!t||t.top!==n.top||t.bottom!==n.bottom;f[e].oldData=n,o&&i.onResize(),t&&i.onScroll()}),p.window.requestAnimationFrame(y))}function v(t,e){for(var i,n=(t=("object"===("undefined"==typeof HTMLElement?"undefined":l(HTMLElement))?t instanceof HTMLElement:t&&"object"===l(t)&&null!==t&&1===t.nodeType&&"string"==typeof t.nodeName)?[t]:t).length,o=0,s=arguments.length,r=new Array(2<s?s-2:0),a=2;a<s;a++)r[a-2]=arguments[a];for(;o<n;o+=1)if("object"===l(e)||void 0===e?t[o].jarallax||(t[o].jarallax=new x(t[o],e)):t[o].jarallax&&(i=t[o].jarallax[e].apply(t[o].jarallax,r)),void 0!==i)return i;return t}var w=0,x=(s(_.prototype,[{key:"css",value:function(e,i){return"string"==typeof i?p.window.getComputedStyle(e).getPropertyValue(i):(i.transform&&d&&(i[d]=i.transform),Object.keys(i).forEach(function(t){e.style[t]=i[t]}),e)}},{key:"extend",value:function(i){for(var t=arguments.length,n=new Array(1<t?t-1:0),e=1;e<t;e++)n[e-1]=arguments[e];return i=i||{},Object.keys(n).forEach(function(e){n[e]&&Object.keys(n[e]).forEach(function(t){i[t]=n[e][t]})}),i}},{key:"getWindowData",value:function(){return{width:p.window.innerWidth||document.documentElement.clientWidth,height:m,y:document.documentElement.scrollTop}}},{key:"initImg",value:function(){var t=this,e=t.options.imgElement;return(e=e&&"string"==typeof e?t.$item.querySelector(e):e)instanceof Element||(t.options.imgSrc?(e=new Image).src=t.options.imgSrc:e=null),e&&(t.options.keepImg?t.image.$item=e.cloneNode(!0):(t.image.$item=e,t.image.$itemParent=e.parentNode),t.image.useImgTag=!0),!(!t.image.$item&&(null===t.image.src&&(t.image.src="data:image/gif;base64,R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7",t.image.bgImage=t.css(t.$item,"background-image")),!t.image.bgImage||"none"===t.image.bgImage))}},{key:"canInitParallax",value:function(){return d&&!this.options.disableParallax()}},{key:"init",value:function(){var t,e=this,i={position:"absolute",top:0,left:0,width:"100%",height:"100%",overflow:"hidden"},n={pointerEvents:"none",transformStyle:"preserve-3d",backfaceVisibility:"hidden",willChange:"transform,opacity"};!e.options.keepImg&&((t=e.$item.getAttribute("style"))&&e.$item.setAttribute("data-jarallax-original-styles",t),e.image.useImgTag)&&(t=e.image.$item.getAttribute("style"))&&e.image.$item.setAttribute("data-jarallax-original-styles",t),"static"===e.css(e.$item,"position")&&e.css(e.$item,{position:"relative"}),"auto"===e.css(e.$item,"z-index")&&e.css(e.$item,{zIndex:0}),e.image.$container=document.createElement("div"),e.css(e.image.$container,i),e.css(e.image.$container,{"z-index":e.options.zIndex}),h&&e.css(e.image.$container,{opacity:.9999}),e.image.$container.setAttribute("id","jarallax-container-".concat(e.instanceID)),e.$item.appendChild(e.image.$container),e.image.useImgTag?n=e.extend({"object-fit":e.options.imgSize,"object-position":e.options.imgPosition,"font-family":"object-fit: ".concat(e.options.imgSize,"; object-position: ").concat(e.options.imgPosition,";"),"max-width":"none"},i,n):(e.image.$item=document.createElement("div"),e.image.src&&(n=e.extend({"background-position":e.options.imgPosition,"background-size":e.options.imgSize,"background-repeat":e.options.imgRepeat,"background-image":e.image.bgImage||'url("'.concat(e.image.src,'")')},i,n))),"opacity"!==e.options.type&&"scale"!==e.options.type&&"scale-opacity"!==e.options.type&&1!==e.options.speed||(e.image.position="absolute"),"fixed"===e.image.position&&(t=(t=>{for(var e=[];null!==t.parentElement;)1===(t=t.parentElement).nodeType&&e.push(t);return e})(e.$item).filter(function(t){var t=p.window.getComputedStyle(t),e=t["-webkit-transform"]||t["-moz-transform"]||t.transform;return e&&"none"!==e||/(auto|scroll)/.test(t.overflow+t["overflow-y"]+t["overflow-x"])}),e.image.position=t.length?"absolute":"fixed"),n.position=e.image.position,e.css(e.image.$item,n),e.image.$container.appendChild(e.image.$item),e.onResize(),e.onScroll(!0),e.options.onInit&&e.options.onInit.call(e),"none"!==e.css(e.$item,"background-image")&&e.css(e.$item,{"background-image":"none"}),e.addToParallaxList()}},{key:"addToParallaxList",value:function(){f.push({instance:this}),1===f.length&&p.window.requestAnimationFrame(y)}},{key:"removeFromParallaxList",value:function(){var i=this;f.forEach(function(t,e){t.instance.instanceID===i.instanceID&&f.splice(e,1)})}},{key:"destroy",value:function(){var t=this;t.removeFromParallaxList();var e,i=t.$item.getAttribute("data-jarallax-original-styles");t.$item.removeAttribute("data-jarallax-original-styles"),i?t.$item.setAttribute("style",i):t.$item.removeAttribute("style"),t.image.useImgTag&&(e=t.image.$item.getAttribute("data-jarallax-original-styles"),t.image.$item.removeAttribute("data-jarallax-original-styles"),e?t.image.$item.setAttribute("style",i):t.image.$item.removeAttribute("style"),t.image.$itemParent)&&t.image.$itemParent.appendChild(t.image.$item),t.$clipStyles&&t.$clipStyles.parentNode.removeChild(t.$clipStyles),t.image.$container&&t.image.$container.parentNode.removeChild(t.image.$container),t.options.onDestroy&&t.options.onDestroy.call(t),delete t.$item.jarallax}},{key:"clipContainer",value:function(){var t,e,i;"fixed"===this.image.position&&(i=(e=(t=this).image.$container.getBoundingClientRect()).width,e=e.height,t.$clipStyles||(t.$clipStyles=document.createElement("style"),t.$clipStyles.setAttribute("type","text/css"),t.$clipStyles.setAttribute("id","jarallax-clip-".concat(t.instanceID)),(document.head||document.getElementsByTagName("head")[0]).appendChild(t.$clipStyles)),i="#jarallax-container-".concat(t.instanceID," {\n            clip: rect(0 ").concat(i,"px ").concat(e,"px 0);\n            clip: rect(0, ").concat(i,"px, ").concat(e,"px, 0);\n            -webkit-clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);\n            clip-path: polygon(0 0, 100% 0, 100% 100%, 0 100%);\n        }"),t.$clipStyles.styleSheet?t.$clipStyles.styleSheet.cssText=i:t.$clipStyles.innerHTML=i)}},{key:"coverImage",value:function(){var t=this,e=t.image.$container.getBoundingClientRect(),i=e.height,n=t.options.speed,o="scroll"===t.options.type||"scroll-opacity"===t.options.type,s=0,r=i;return o&&(n<0?(s=n*Math.max(i,m),m<i&&(s-=n*(i-m))):s=n*(i+m),1<n?r=Math.abs(s-m):n<0?r=s/n+Math.abs(s):r+=(m-i)*(1-n),s/=2),t.parallaxScrollDistance=s,n=o?(m-r)/2:(i-r)/2,t.css(t.image.$item,{height:"".concat(r,"px"),marginTop:"".concat(n,"px"),left:"fixed"===t.image.position?"".concat(e.left,"px"):"0",width:"".concat(e.width,"px")}),t.options.onCoverImage&&t.options.onCoverImage.call(t),{image:{height:r,marginTop:n},container:e}}},{key:"isVisible",value:function(){return this.isElementInViewport||!1}},{key:"onScroll",value:function(t){var e,i,n,o,s,r,a=this,l=a.$item.getBoundingClientRect(),c=l.top,h=l.height,u={},d=l;a.options.elementInViewport&&(d=a.options.elementInViewport.getBoundingClientRect()),a.isElementInViewport=0<=d.bottom&&0<=d.right&&d.top<=m&&d.left<=p.window.innerWidth,(t||a.isElementInViewport)&&(d=Math.max(0,c),t=Math.max(0,h+c),e=Math.max(0,-c),i=Math.max(0,c+h-m),n=Math.max(0,h-(c+h-m)),o=Math.max(0,-c+m-h),s=1-(m-c)/(m+h)*2,r=1,h<m?r=1-(e||i)/h:t<=m?r=t/m:n<=m&&(r=n/m),"opacity"!==a.options.type&&"scale-opacity"!==a.options.type&&"scroll-opacity"!==a.options.type||(u.transform="translate3d(0,0,0)",u.opacity=r),"scale"!==a.options.type&&"scale-opacity"!==a.options.type||(h=1,a.options.speed<0?h-=a.options.speed*r:h+=a.options.speed*(1-r),u.transform="scale(".concat(h,") translate3d(0,0,0)")),"scroll"!==a.options.type&&"scroll-opacity"!==a.options.type||(h=a.parallaxScrollDistance*s,"absolute"===a.image.position&&(h-=c),u.transform="translate3d(0,".concat(h,"px,0)")),a.css(a.image.$item,u),a.options.onScroll)&&a.options.onScroll.call(a,{section:l,beforeTop:d,beforeTopEnd:t,afterTop:e,beforeBottom:i,beforeBottomEnd:n,afterBottom:o,visiblePercent:r,fromViewportCenter:s})}},{key:"onResize",value:function(){this.coverImage(),this.clipContainer()}}]),_);function _(t,e){if(!(this instanceof _))throw new TypeError("Cannot call a class as a function");var i=this;i.instanceID=w,w+=1,i.$item=t,i.defaults={type:"scroll",speed:.5,imgSrc:null,imgElement:".jarallax-img",imgSize:"cover",imgPosition:"50% 50%",imgRepeat:"no-repeat",keepImg:!1,elementInViewport:null,zIndex:-100,disableParallax:!1,disableVideo:!1,videoSrc:null,videoStartTime:0,videoEndTime:0,videoVolume:0,videoLoop:!0,videoPlayOnlyVisible:!0,videoLazyLoading:!0,onScroll:null,onInit:null,onDestroy:null,onCoverImage:null};var n,o,s=i.$item.dataset||{},r={},t=(Object.keys(s).forEach(function(t){var e=t.substr(0,1).toLowerCase()+t.substr(1);e&&void 0!==i.defaults[e]&&(r[e]=s[t])}),i.options=i.extend({},i.defaults,r,e),i.pureOptions=i.extend({},i.options),Object.keys(i.options).forEach(function(t){"true"===i.options[t]?i.options[t]=!0:"false"===i.options[t]&&(i.options[t]=!1)}),i.options.speed=Math.min(2,Math.max(-1,parseFloat(i.options.speed))),"string"==typeof i.options.disableParallax&&(i.options.disableParallax=new RegExp(i.options.disableParallax)),i.options.disableParallax instanceof RegExp&&(n=i.options.disableParallax,i.options.disableParallax=function(){return n.test(c.userAgent)}),"function"!=typeof i.options.disableParallax&&(i.options.disableParallax=function(){return!1}),"string"==typeof i.options.disableVideo&&(i.options.disableVideo=new RegExp(i.options.disableVideo)),i.options.disableVideo instanceof RegExp&&(o=i.options.disableVideo,i.options.disableVideo=function(){return o.test(c.userAgent)}),"function"!=typeof i.options.disableVideo&&(i.options.disableVideo=function(){return!1}),i.options.elementInViewport);(t=t&&"object"===l(t)&&void 0!==t.length?a(t,1)[0]:t)instanceof Element||(t=null),i.options.elementInViewport=t,i.image={src:i.options.imgSrc||null,$container:null,useImgTag:!1,position:/iPad|iPhone|iPod|Android/.test(c.userAgent)?"absolute":"fixed"},i.initImg()&&i.canInitParallax()&&i.init()}v.constructor=x,e.default=v}]),((l,i,n,a)=>{function c(t,e){this.settings=null,this.options=l.extend({},c.Defaults,e),this.$element=l(t),this._handlers={},this._plugins={},this._supress={},this._current=null,this._speed=null,this._coordinates=[],this._breakpoint=null,this._width=null,this._items=[],this._clones=[],this._mergers=[],this._widths=[],this._invalidated={},this._pipe=[],this._drag={time:null,target:null,pointer:null,stage:{start:null,current:null},direction:null},this._states={current:{},tags:{initializing:["busy"],animating:["busy"],dragging:["interacting"]}},l.each(["onResize","onThrottledResize"],l.proxy(function(t,e){this._handlers[e]=l.proxy(this[e],this)},this)),l.each(c.Plugins,l.proxy(function(t,e){this._plugins[t.charAt(0).toLowerCase()+t.slice(1)]=new e(this)},this)),l.each(c.Workers,l.proxy(function(t,e){this._pipe.push({filter:e.filter,run:l.proxy(e.run,this)})},this)),this.setup(),this.initialize()}c.Defaults={items:3,loop:!1,center:!1,rewind:!1,checkVisibility:!0,mouseDrag:!0,touchDrag:!0,pullDrag:!0,freeDrag:!1,margin:0,stagePadding:0,merge:!1,mergeFit:!0,autoWidth:!1,startPosition:0,rtl:!1,smartSpeed:250,fluidSpeed:!1,dragEndSpeed:!1,responsive:{},responsiveRefreshRate:200,responsiveBaseElement:i,fallbackEasing:"swing",slideTransition:"",info:!1,nestedItemSelector:!1,itemElement:"div",stageElement:"div",refreshClass:"owl-refresh",loadedClass:"owl-loaded",loadingClass:"owl-loading",rtlClass:"owl-rtl",responsiveClass:"owl-responsive",dragClass:"owl-drag",itemClass:"owl-item",stageClass:"owl-stage",stageOuterClass:"owl-stage-outer",grabClass:"owl-grab"},c.Width={Default:"default",Inner:"inner",Outer:"outer"},c.Type={Event:"event",State:"state"},c.Plugins={},c.Workers=[{filter:["width","settings"],run:function(){this._width=this.$element.width()}},{filter:["width","items","settings"],run:function(t){t.current=this._items&&this._items[this.relative(this._current)]}},{filter:["items","settings"],run:function(){this.$stage.children(".cloned").remove()}},{filter:["width","items","settings"],run:function(t){var e=this.settings.margin||"",i=!this.settings.autoWidth,n=this.settings.rtl,n={width:"auto","margin-left":n?e:"","margin-right":n?"":e};i||this.$stage.children().css(n),t.css=n}},{filter:["width","items","settings"],run:function(t){var e,i=(this.width()/this.settings.items).toFixed(3)-this.settings.margin,n=this._items.length,o=!this.settings.autoWidth,s=[];for(t.items={merge:!1,width:i};n--;)e=this._mergers[n],e=this.settings.mergeFit&&Math.min(e,this.settings.items)||e,t.items.merge=1<e||t.items.merge,s[n]=o?i*e:this._items[n].width();this._widths=s}},{filter:["items","settings"],run:function(){var t=[],e=this._items,i=this.settings,n=Math.max(2*i.items,4),o=2*Math.ceil(e.length/2),s=i.loop&&e.length?i.rewind?n:Math.max(n,o):0,r="",a="";for(s/=2;0<s;)t.push(this.normalize(t.length/2,!0)),r+=e[t[t.length-1]][0].outerHTML,t.push(this.normalize(e.length-1-(t.length-1)/2,!0)),a=e[t[t.length-1]][0].outerHTML+a,--s;this._clones=t,l(r).addClass("cloned").appendTo(this.$stage),l(a).addClass("cloned").prependTo(this.$stage)}},{filter:["width","items","settings"],run:function(){for(var t,e,i=this.settings.rtl?1:-1,n=this._clones.length+this._items.length,o=-1,s=[];++o<n;)t=s[o-1]||0,e=this._widths[this.relative(o)]+this.settings.margin,s.push(t+e*i);this._coordinates=s}},{filter:["width","items","settings"],run:function(){var t=this.settings.stagePadding,e=this._coordinates,e={width:Math.ceil(Math.abs(e[e.length-1]))+2*t,"padding-left":t||"","padding-right":t||""};this.$stage.css(e)}},{filter:["width","items","settings"],run:function(t){var e=this._coordinates.length,i=!this.settings.autoWidth,n=this.$stage.children();if(i&&t.items.merge)for(;e--;)t.css.width=this._widths[this.relative(e)],n.eq(e).css(t.css);else i&&(t.css.width=t.items.width,n.css(t.css))}},{filter:["items"],run:function(){this._coordinates.length<1&&this.$stage.removeAttr("style")}},{filter:["width","items","settings"],run:function(t){t.current=t.current?this.$stage.children().index(t.current):0,t.current=Math.max(this.minimum(),Math.min(this.maximum(),t.current)),this.reset(t.current)}},{filter:["position"],run:function(){this.animate(this.coordinates(this._current))}},{filter:["width","position","items","settings"],run:function(){for(var t,e,i=this.settings.rtl?1:-1,n=2*this.settings.stagePadding,o=this.coordinates(this.current())+n,s=o+this.width()*i,r=[],a=0,l=this._coordinates.length;a<l;a++)t=this._coordinates[a-1]||0,e=Math.abs(this._coordinates[a])+n*i,(this.op(t,"<=",o)&&this.op(t,">",s)||this.op(e,"<",o)&&this.op(e,">",s))&&r.push(a);this.$stage.children(".active").removeClass("active"),this.$stage.children(":eq("+r.join("), :eq(")+")").addClass("active"),this.$stage.children(".center").removeClass("center"),this.settings.center&&this.$stage.children().eq(this.current()).addClass("center")}}],c.prototype.initializeStage=function(){this.$stage=this.$element.find("."+this.settings.stageClass),this.$stage.length||(this.$element.addClass(this.options.loadingClass),this.$stage=l("<"+this.settings.stageElement+">",{class:this.settings.stageClass}).wrap(l("<div/>",{class:this.settings.stageOuterClass})),this.$element.append(this.$stage.parent()))},c.prototype.initializeItems=function(){var t=this.$element.find(".owl-item");t.length?(this._items=t.get().map(function(t){return l(t)}),this._mergers=this._items.map(function(){return 1}),this.refresh()):(this.replace(this.$element.children().not(this.$stage.parent())),this.isVisible()?this.refresh():this.invalidate("width"),this.$element.removeClass(this.options.loadingClass).addClass(this.options.loadedClass))},c.prototype.initialize=function(){var t,e;this.enter("initializing"),this.trigger("initialize"),this.$element.toggleClass(this.settings.rtlClass,this.settings.rtl),this.settings.autoWidth&&!this.is("pre-loading")&&(t=this.$element.find("img"),e=this.settings.nestedItemSelector?"."+this.settings.nestedItemSelector:a,e=this.$element.children(e).width(),t.length)&&e<=0&&this.preloadAutoWidthImages(t),this.initializeStage(),this.initializeItems(),this.registerEventHandlers(),this.leave("initializing"),this.trigger("initialized")},c.prototype.isVisible=function(){return!this.settings.checkVisibility||this.$element.is(":visible")},c.prototype.setup=function(){var e=this.viewport(),t=this.options.responsive,i=-1,n=null;t?(l.each(t,function(t){t<=e&&i<t&&(i=Number(t))}),"function"==typeof(n=l.extend({},this.options,t[i])).stagePadding&&(n.stagePadding=n.stagePadding()),delete n.responsive,n.responsiveClass&&this.$element.attr("class",this.$element.attr("class").replace(new RegExp("("+this.options.responsiveClass+"-)\\S+\\s","g"),"$1"+i))):n=l.extend({},this.options),this.trigger("change",{property:{name:"settings",value:n}}),this._breakpoint=i,this.settings=n,this.invalidate("settings"),this.trigger("changed",{property:{name:"settings",value:this.settings}})},c.prototype.optionsLogic=function(){this.settings.autoWidth&&(this.settings.stagePadding=!1,this.settings.merge=!1)},c.prototype.prepare=function(t){var e=this.trigger("prepare",{content:t});return e.data||(e.data=l("<"+this.settings.itemElement+"/>").addClass(this.options.itemClass).append(t)),this.trigger("prepared",{content:e.data}),e.data},c.prototype.update=function(){for(var t=0,e=this._pipe.length,i=l.proxy(function(t){return this[t]},this._invalidated),n={};t<e;)(this._invalidated.all||0<l.grep(this._pipe[t].filter,i).length)&&this._pipe[t].run(n),t++;this._invalidated={},this.is("valid")||this.enter("valid")},c.prototype.width=function(t){switch(t=t||c.Width.Default){case c.Width.Inner:case c.Width.Outer:return this._width;default:return this._width-2*this.settings.stagePadding+this.settings.margin}},c.prototype.refresh=function(){this.enter("refreshing"),this.trigger("refresh"),this.setup(),this.optionsLogic(),this.$element.addClass(this.options.refreshClass),this.update(),this.$element.removeClass(this.options.refreshClass),this.leave("refreshing"),this.trigger("refreshed")},c.prototype.onThrottledResize=function(){i.clearTimeout(this.resizeTimer),this.resizeTimer=i.setTimeout(this._handlers.onResize,this.settings.responsiveRefreshRate)},c.prototype.onResize=function(){return!!this._items.length&&this._width!==this.$element.width()&&!!this.isVisible()&&(this.enter("resizing"),this.trigger("resize").isDefaultPrevented()?(this.leave("resizing"),!1):(this.invalidate("width"),this.refresh(),this.leave("resizing"),void this.trigger("resized")))},c.prototype.registerEventHandlers=function(){l.support.transition&&this.$stage.on(l.support.transition.end+".owl.core",l.proxy(this.onTransitionEnd,this)),!1!==this.settings.responsive&&this.on(i,"resize",this._handlers.onThrottledResize),this.settings.mouseDrag&&(this.$element.addClass(this.options.dragClass),this.$stage.on("mousedown.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("dragstart.owl.core selectstart.owl.core",function(){return!1})),this.settings.touchDrag&&(this.$stage.on("touchstart.owl.core",l.proxy(this.onDragStart,this)),this.$stage.on("touchcancel.owl.core",l.proxy(this.onDragEnd,this)))},c.prototype.onDragStart=function(t){var e=null;3!==t.which&&(e=l.support.transform?{x:(e=this.$stage.css("transform").replace(/.*\(|\)| /g,"").split(","))[16===e.length?12:4],y:e[16===e.length?13:5]}:(e=this.$stage.position(),{x:this.settings.rtl?e.left+this.$stage.width()-this.width()+this.settings.margin:e.left,y:e.top}),this.is("animating")&&(l.support.transform?this.animate(e.x):this.$stage.stop(),this.invalidate("position")),this.$element.toggleClass(this.options.grabClass,"mousedown"===t.type),this.speed(0),this._drag.time=(new Date).getTime(),this._drag.target=l(t.target),this._drag.stage.start=e,this._drag.stage.current=e,this._drag.pointer=this.pointer(t),l(n).on("mouseup.owl.core touchend.owl.core",l.proxy(this.onDragEnd,this)),l(n).one("mousemove.owl.core touchmove.owl.core",l.proxy(function(t){var e=this.difference(this._drag.pointer,this.pointer(t));l(n).on("mousemove.owl.core touchmove.owl.core",l.proxy(this.onDragMove,this)),Math.abs(e.x)<Math.abs(e.y)&&this.is("valid")||(t.preventDefault(),this.enter("dragging"),this.trigger("drag"))},this)))},c.prototype.onDragMove=function(t){var e=null,i=null,n=this.difference(this._drag.pointer,this.pointer(t)),o=this.difference(this._drag.stage.start,n);this.is("dragging")&&(t.preventDefault(),this.settings.loop?(e=this.coordinates(this.minimum()),i=this.coordinates(this.maximum()+1)-e,o.x=((o.x-e)%i+i)%i+e):(e=this.settings.rtl?this.coordinates(this.maximum()):this.coordinates(this.minimum()),i=this.settings.rtl?this.coordinates(this.minimum()):this.coordinates(this.maximum()),t=this.settings.pullDrag?-1*n.x/5:0,o.x=Math.max(Math.min(o.x,e+t),i+t)),this._drag.stage.current=o,this.animate(o.x))},c.prototype.onDragEnd=function(t){var t=this.difference(this._drag.pointer,this.pointer(t)),e=this._drag.stage.current,i=0<t.x^this.settings.rtl?"left":"right";l(n).off(".owl.core"),this.$element.removeClass(this.options.grabClass),(0!==t.x&&this.is("dragging")||!this.is("valid"))&&(this.speed(this.settings.dragEndSpeed||this.settings.smartSpeed),this.current(this.closest(e.x,0!==t.x?i:this._drag.direction)),this.invalidate("position"),this.update(),this._drag.direction=i,3<Math.abs(t.x)||300<(new Date).getTime()-this._drag.time)&&this._drag.target.one("click.owl.core",function(){return!1}),this.is("dragging")&&(this.leave("dragging"),this.trigger("dragged"))},c.prototype.closest=function(i,n){var o=-1,s=this.width(),r=this.coordinates();return this.settings.freeDrag||l.each(r,l.proxy(function(t,e){return"left"===n&&e-30<i&&i<e+30?o=t:"right"===n&&e-s-30<i&&i<e-s+30?o=t+1:this.op(i,"<",e)&&this.op(i,">",r[t+1]!==a?r[t+1]:e-s)&&(o="left"===n?t+1:t),-1===o},this)),this.settings.loop||(this.op(i,">",r[this.minimum()])?o=i=this.minimum():this.op(i,"<",r[this.maximum()])&&(o=i=this.maximum())),o},c.prototype.animate=function(t){var e=0<this.speed();this.is("animating")&&this.onTransitionEnd(),e&&(this.enter("animating"),this.trigger("translate")),l.support.transform3d&&l.support.transition?this.$stage.css({transform:"translate3d("+t+"px,0px,0px)",transition:this.speed()/1e3+"s"+(this.settings.slideTransition?" "+this.settings.slideTransition:"")}):e?this.$stage.animate({left:t+"px"},this.speed(),this.settings.fallbackEasing,l.proxy(this.onTransitionEnd,this)):this.$stage.css({left:t+"px"})},c.prototype.is=function(t){return this._states.current[t]&&0<this._states.current[t]},c.prototype.current=function(t){if(t!==a){if(0===this._items.length)return a;var e;t=this.normalize(t),this._current!==t&&((e=this.trigger("change",{property:{name:"position",value:t}})).data!==a&&(t=this.normalize(e.data)),this._current=t,this.invalidate("position"),this.trigger("changed",{property:{name:"position",value:this._current}}))}return this._current},c.prototype.invalidate=function(t){return"string"===l.type(t)&&(this._invalidated[t]=!0,this.is("valid"))&&this.leave("valid"),l.map(this._invalidated,function(t,e){return e})},c.prototype.reset=function(t){(t=this.normalize(t))!==a&&(this._speed=0,this._current=t,this.suppress(["translate","translated"]),this.animate(this.coordinates(t)),this.release(["translate","translated"]))},c.prototype.normalize=function(t,e){var i=this._items.length,e=e?0:this._clones.length;return!this.isNumeric(t)||i<1?t=a:(t<0||i+e<=t)&&(t=((t-e/2)%i+i)%i+e/2),t},c.prototype.relative=function(t){return t-=this._clones.length/2,this.normalize(t,!0)},c.prototype.maximum=function(t){var e,i,n,o=this.settings,s=this._coordinates.length;if(o.loop)s=this._clones.length/2+this._items.length-1;else if(o.autoWidth||o.merge){if(e=this._items.length)for(i=this._items[--e].width(),n=this.$element.width();e--&&!((i+=this._items[e].width()+this.settings.margin)>n););s=e+1}else s=o.center?this._items.length-1:this._items.length-o.items;return t&&(s-=this._clones.length/2),Math.max(s,0)},c.prototype.minimum=function(t){return t?0:this._clones.length/2},c.prototype.items=function(t){return t===a?this._items.slice():(t=this.normalize(t,!0),this._items[t])},c.prototype.mergers=function(t){return t===a?this._mergers.slice():(t=this.normalize(t,!0),this._mergers[t])},c.prototype.clones=function(i){function n(t){return t%2==0?o+t/2:e-(t+1)/2}var e=this._clones.length/2,o=e+this._items.length;return i===a?l.map(this._clones,function(t,e){return n(e)}):l.map(this._clones,function(t,e){return t===i?n(e):null})},c.prototype.speed=function(t){return t!==a&&(this._speed=t),this._speed},c.prototype.coordinates=function(t){var e,i=1,n=t-1;return t===a?l.map(this._coordinates,l.proxy(function(t,e){return this.coordinates(e)},this)):(this.settings.center?(this.settings.rtl&&(i=-1,n=t+1),e=this._coordinates[t],e+=(this.width()-e+(this._coordinates[n]||0))/2*i):e=this._coordinates[n]||0,Math.ceil(e))},c.prototype.duration=function(t,e,i){return 0===i?0:Math.min(Math.max(Math.abs(e-t),1),6)*Math.abs(i||this.settings.smartSpeed)},c.prototype.to=function(t,e){var i=this.current(),n=t-this.relative(i),o=(0<n)-(n<0),s=this._items.length,r=this.minimum(),a=this.maximum();this.settings.loop?(!this.settings.rewind&&Math.abs(n)>s/2&&(n+=-1*o*s),(o=(((t=i+n)-r)%s+s)%s+r)!==t&&o-n<=a&&0<o-n&&this.reset(i=(t=o)-n)):t=this.settings.rewind?(t%(a+=1)+a)%a:Math.max(r,Math.min(a,t)),this.speed(this.duration(i,t,e)),this.current(t),this.isVisible()&&this.update()},c.prototype.next=function(t){t=t||!1,this.to(this.relative(this.current())+1,t)},c.prototype.prev=function(t){t=t||!1,this.to(this.relative(this.current())-1,t)},c.prototype.onTransitionEnd=function(t){if(t!==a&&(t.stopPropagation(),(t.target||t.srcElement||t.originalTarget)!==this.$stage.get(0)))return!1;this.leave("animating"),this.trigger("translated")},c.prototype.viewport=function(){var t;return this.options.responsiveBaseElement!==i?t=l(this.options.responsiveBaseElement).width():i.innerWidth?t=i.innerWidth:n.documentElement&&n.documentElement.clientWidth?t=n.documentElement.clientWidth:console.warn("Can not detect viewport width."),t},c.prototype.replace=function(t){this.$stage.empty(),this._items=[],t=t&&(t instanceof jQuery?t:l(t)),(t=this.settings.nestedItemSelector?t.find("."+this.settings.nestedItemSelector):t).filter(function(){return 1===this.nodeType}).each(l.proxy(function(t,e){e=this.prepare(e),this.$stage.append(e),this._items.push(e),this._mergers.push(+e.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)},this)),this.reset(this.isNumeric(this.settings.startPosition)?this.settings.startPosition:0),this.invalidate("items")},c.prototype.add=function(t,e){var i=this.relative(this._current);e=e===a?this._items.length:this.normalize(e,!0),t=t instanceof jQuery?t:l(t),this.trigger("add",{content:t,position:e}),t=this.prepare(t),0===this._items.length||e===this._items.length?(0===this._items.length&&this.$stage.append(t),0!==this._items.length&&this._items[e-1].after(t),this._items.push(t),this._mergers.push(+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)):(this._items[e].before(t),this._items.splice(e,0,t),this._mergers.splice(e,0,+t.find("[data-merge]").addBack("[data-merge]").attr("data-merge")||1)),this._items[i]&&this.reset(this._items[i].index()),this.invalidate("items"),this.trigger("added",{content:t,position:e})},c.prototype.remove=function(t){(t=this.normalize(t,!0))!==a&&(this.trigger("remove",{content:this._items[t],position:t}),this._items[t].remove(),this._items.splice(t,1),this._mergers.splice(t,1),this.invalidate("items"),this.trigger("removed",{content:null,position:t}))},c.prototype.preloadAutoWidthImages=function(t){t.each(l.proxy(function(t,e){this.enter("pre-loading"),e=l(e),l(new Image).one("load",l.proxy(function(t){e.attr("src",t.target.src),e.css("opacity",1),this.leave("pre-loading"),this.is("pre-loading")||this.is("initializing")||this.refresh()},this)).attr("src",e.attr("src")||e.attr("data-src")||e.attr("data-src-retina"))},this))},c.prototype.destroy=function(){for(var t in this.$element.off(".owl.core"),this.$stage.off(".owl.core"),l(n).off(".owl.core"),!1!==this.settings.responsive&&(i.clearTimeout(this.resizeTimer),this.off(i,"resize",this._handlers.onThrottledResize)),this._plugins)this._plugins[t].destroy();this.$stage.children(".cloned").remove(),this.$stage.unwrap(),this.$stage.children().contents().unwrap(),this.$stage.children().unwrap(),this.$stage.remove(),this.$element.removeClass(this.options.refreshClass).removeClass(this.options.loadingClass).removeClass(this.options.loadedClass).removeClass(this.options.rtlClass).removeClass(this.options.dragClass).removeClass(this.options.grabClass).attr("class",this.$element.attr("class").replace(new RegExp(this.options.responsiveClass+"-\\S+\\s","g"),"")).removeData("owl.carousel")},c.prototype.op=function(t,e,i){var n=this.settings.rtl;switch(e){case"<":return n?i<t:t<i;case">":return n?t<i:i<t;case">=":return n?t<=i:i<=t;case"<=":return n?i<=t:t<=i}},c.prototype.on=function(t,e,i,n){t.addEventListener?t.addEventListener(e,i,n):t.attachEvent&&t.attachEvent("on"+e,i)},c.prototype.off=function(t,e,i,n){t.removeEventListener?t.removeEventListener(e,i,n):t.detachEvent&&t.detachEvent("on"+e,i)},c.prototype.trigger=function(t,e,i,n,o){var s={item:{count:this._items.length,index:this.current()}},r=l.camelCase(l.grep(["on",t,i],function(t){return t}).join("-").toLowerCase()),a=l.Event([t,"owl",i||"carousel"].join(".").toLowerCase(),l.extend({relatedTarget:this},s,e));return this._supress[t]||(l.each(this._plugins,function(t,e){e.onTrigger&&e.onTrigger(a)}),this.register({type:c.Type.Event,name:t}),this.$element.trigger(a),this.settings&&"function"==typeof this.settings[r]&&this.settings[r].call(this,a)),a},c.prototype.enter=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]===a&&(this._states.current[e]=0),this._states.current[e]++},this))},c.prototype.leave=function(t){l.each([t].concat(this._states.tags[t]||[]),l.proxy(function(t,e){this._states.current[e]--},this))},c.prototype.register=function(i){var e;i.type===c.Type.Event?(l.event.special[i.name]||(l.event.special[i.name]={}),l.event.special[i.name].owl||(e=l.event.special[i.name]._default,l.event.special[i.name]._default=function(t){return!e||!e.apply||t.namespace&&-1!==t.namespace.indexOf("owl")?t.namespace&&-1<t.namespace.indexOf("owl"):e.apply(this,arguments)},l.event.special[i.name].owl=!0)):i.type===c.Type.State&&(this._states.tags[i.name]?this._states.tags[i.name]=this._states.tags[i.name].concat(i.tags):this._states.tags[i.name]=i.tags,this._states.tags[i.name]=l.grep(this._states.tags[i.name],l.proxy(function(t,e){return l.inArray(t,this._states.tags[i.name])===e},this)))},c.prototype.suppress=function(t){l.each(t,l.proxy(function(t,e){this._supress[e]=!0},this))},c.prototype.release=function(t){l.each(t,l.proxy(function(t,e){delete this._supress[e]},this))},c.prototype.pointer=function(t){var e={x:null,y:null};return(t=(t=t.originalEvent||t||i.event).touches&&t.touches.length?t.touches[0]:t.changedTouches&&t.changedTouches.length?t.changedTouches[0]:t).pageX?(e.x=t.pageX,e.y=t.pageY):(e.x=t.clientX,e.y=t.clientY),e},c.prototype.isNumeric=function(t){return!isNaN(parseFloat(t))},c.prototype.difference=function(t,e){return{x:t.x-e.x,y:t.y-e.y}},l.fn.owlCarousel=function(e){var n=Array.prototype.slice.call(arguments,1);return this.each(function(){var t=l(this),i=t.data("owl.carousel");i||(i=new c(this,"object"==typeof e&&e),t.data("owl.carousel",i),l.each(["next","prev","to","destroy","refresh","replace","add","remove"],function(t,e){i.register({type:c.Type.Event,name:e}),i.$element.on(e+".owl.carousel.core",l.proxy(function(t){t.namespace&&t.relatedTarget!==this&&(this.suppress([e]),i[e].apply(this,[].slice.call(arguments,1)),this.release([e]))},i))})),"string"==typeof e&&"_"!==e.charAt(0)&&i[e].apply(i,n)})},l.fn.owlCarousel.Constructor=c})(window.Zepto||window.jQuery,window,document),((e,i)=>{function n(t){this._core=t,this._interval=null,this._visible=null,this._handlers={"initialized.owl.carousel":e.proxy(function(t){t.namespace&&this._core.settings.autoRefresh&&this.watch()},this)},this._core.options=e.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers)}n.Defaults={autoRefresh:!0,autoRefreshInterval:500},n.prototype.watch=function(){this._interval||(this._visible=this._core.isVisible(),this._interval=i.setInterval(e.proxy(this.refresh,this),this._core.settings.autoRefreshInterval))},n.prototype.refresh=function(){this._core.isVisible()!==this._visible&&(this._visible=!this._visible,this._core.$element.toggleClass("owl-hidden",!this._visible),this._visible)&&this._core.invalidate("width")&&this._core.refresh()},n.prototype.destroy=function(){var t,e;for(t in i.clearInterval(this._interval),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},e.fn.owlCarousel.Constructor.Plugins.AutoRefresh=n})(window.Zepto||window.jQuery,window,document),((a,o)=>{function e(t){this._core=t,this._loaded=[],this._handlers={"initialized.owl.carousel change.owl.carousel resized.owl.carousel":a.proxy(function(t){if(t.namespace&&this._core.settings&&this._core.settings.lazyLoad&&(t.property&&"position"==t.property.name||"initialized"==t.type)){var e=this._core.settings,i=e.center&&Math.ceil(e.items/2)||e.items,n=e.center&&-1*i||0,o=(t.property&&void 0!==t.property.value?t.property.value:this._core.current())+n,s=this._core.clones().length,r=a.proxy(function(t,e){this.load(e)},this);for(0<e.lazyLoadEager&&(i+=e.lazyLoadEager,e.loop)&&(o-=e.lazyLoadEager,i++);n++<i;)this.load(s/2+this._core.relative(o)),s&&a.each(this._core.clones(this._core.relative(o)),r),o++}},this)},this._core.options=a.extend({},e.Defaults,this._core.options),this._core.$element.on(this._handlers)}e.Defaults={lazyLoad:!1,lazyLoadEager:0},e.prototype.load=function(t){var t=this._core.$stage.children().eq(t),e=t&&t.find(".owl-lazy");!e||-1<a.inArray(t.get(0),this._loaded)||(e.each(a.proxy(function(t,e){var i=a(e),n=1<o.devicePixelRatio&&i.attr("data-src-retina")||i.attr("data-src")||i.attr("data-srcset");this._core.trigger("load",{element:i,url:n},"lazy"),i.is("img")?i.one("load.owl.lazy",a.proxy(function(){i.css("opacity",1),this._core.trigger("loaded",{element:i,url:n},"lazy")},this)).attr("src",n):i.is("source")?i.one("load.owl.lazy",a.proxy(function(){this._core.trigger("loaded",{element:i,url:n},"lazy")},this)).attr("srcset",n):((e=new Image).onload=a.proxy(function(){i.css({"background-image":'url("'+n+'")',opacity:"1"}),this._core.trigger("loaded",{element:i,url:n},"lazy")},this),e.src=n)},this)),this._loaded.push(t.get(0)))},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this._core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},a.fn.owlCarousel.Constructor.Plugins.Lazy=e})(window.Zepto||window.jQuery,window,document),((o,i)=>{function n(t){this._core=t,this._previousHeight=null,this._handlers={"initialized.owl.carousel refreshed.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&this.update()},this),"changed.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&"position"===t.property.name&&this.update()},this),"loaded.owl.lazy":o.proxy(function(t){t.namespace&&this._core.settings.autoHeight&&t.element.closest("."+this._core.settings.itemClass).index()===this._core.current()&&this.update()},this)},this._core.options=o.extend({},n.Defaults,this._core.options),this._core.$element.on(this._handlers),this._intervalId=null;var e=this;o(i).on("load",function(){e._core.settings.autoHeight&&e.update()}),o(i).resize(function(){e._core.settings.autoHeight&&(null!=e._intervalId&&clearTimeout(e._intervalId),e._intervalId=setTimeout(function(){e.update()},250))})}n.Defaults={autoHeight:!1,autoHeightClass:"owl-height"},n.prototype.update=function(){var t=this._core._current,e=t+this._core.settings.items,i=this._core.settings.lazyLoad,t=this._core.$stage.children().toArray().slice(t,e),n=[],e=0;o.each(t,function(t,e){n.push(o(e).height())}),(e=Math.max.apply(null,n))<=1&&i&&this._previousHeight&&(e=this._previousHeight),this._previousHeight=e,this._core.$stage.parent().height(e).addClass(this._core.settings.autoHeightClass)},n.prototype.destroy=function(){var t,e;for(t in this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},o.fn.owlCarousel.Constructor.Plugins.AutoHeight=n})(window.Zepto||window.jQuery,window,document),((h,e)=>{function i(t){this._core=t,this._videos={},this._playing=null,this._handlers={"initialized.owl.carousel":h.proxy(function(t){t.namespace&&this._core.register({type:"state",name:"playing",tags:["interacting"]})},this),"resize.owl.carousel":h.proxy(function(t){t.namespace&&this._core.settings.video&&this.isInFullScreen()&&t.preventDefault()},this),"refreshed.owl.carousel":h.proxy(function(t){t.namespace&&this._core.is("resizing")&&this._core.$stage.find(".cloned .owl-video-frame").remove()},this),"changed.owl.carousel":h.proxy(function(t){t.namespace&&"position"===t.property.name&&this._playing&&this.stop()},this),"prepared.owl.carousel":h.proxy(function(t){var e;t.namespace&&(e=h(t.content).find(".owl-video")).length&&(e.css("display","none"),this.fetch(e,h(t.content)))},this)},this._core.options=h.extend({},i.Defaults,this._core.options),this._core.$element.on(this._handlers),this._core.$element.on("click.owl.video",".owl-video-play-icon",h.proxy(function(t){this.play(t)},this))}i.Defaults={video:!1,videoHeight:!1,videoWidth:!1},i.prototype.fetch=function(t,e){var i=t.attr("data-vimeo-id")?"vimeo":t.attr("data-vzaar-id")?"vzaar":"youtube",n=t.attr("data-vimeo-id")||t.attr("data-youtube-id")||t.attr("data-vzaar-id"),o=t.attr("data-width")||this._core.settings.videoWidth,s=t.attr("data-height")||this._core.settings.videoHeight,r=t.attr("href");if(!r)throw new Error("Missing video URL.");if(-1<(n=r.match(/(http:|https:|)\/\/(player.|www.|app.)?(vimeo\.com|youtu(be\.com|\.be|be\.googleapis\.com|be\-nocookie\.com)|vzaar\.com)\/(video\/|videos\/|embed\/|channels\/.+\/|groups\/.+\/|watch\?v=|v\/)?([A-Za-z0-9._%-]*)(\&\S+)?/))[3].indexOf("youtu"))i="youtube";else if(-1<n[3].indexOf("vimeo"))i="vimeo";else{if(!(-1<n[3].indexOf("vzaar")))throw new Error("Video URL not supported.");i="vzaar"}n=n[6],this._videos[r]={type:i,id:n,width:o,height:s},e.attr("data-video",r),this.thumbnail(t,this._videos[r])},i.prototype.thumbnail=function(e,t){function i(t){n=c.lazyLoad?h("<div/>",{class:"owl-video-tn "+l,srcType:t}):h("<div/>",{class:"owl-video-tn",style:"opacity:1;background-image:url("+t+")"}),e.after(n),e.after('<div class="owl-video-play-icon"></div>')}var n,o,s=t.width&&t.height?"width:"+t.width+"px;height:"+t.height+"px;":"",r=e.find("img"),a="src",l="",c=this._core.settings;if(e.wrap(h("<div/>",{class:"owl-video-wrapper",style:s})),this._core.settings.lazyLoad&&(a="data-src",l="owl-lazy"),r.length)return i(r.attr(a)),r.remove(),!1;"youtube"===t.type?(o="//img.youtube.com/vi/"+t.id+"/hqdefault.jpg",i(o)):"vimeo"===t.type?h.ajax({type:"GET",url:"//vimeo.com/api/v2/video/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t[0].thumbnail_large,i(o)}}):"vzaar"===t.type&&h.ajax({type:"GET",url:"//vzaar.com/api/videos/"+t.id+".json",jsonp:"callback",dataType:"jsonp",success:function(t){o=t.framegrab_url,i(o)}})},i.prototype.stop=function(){this._core.trigger("stop",null,"video"),this._playing.find(".owl-video-frame").remove(),this._playing.removeClass("owl-video-playing"),this._playing=null,this._core.leave("playing"),this._core.trigger("stopped",null,"video")},i.prototype.play=function(t){var e,t=h(t.target).closest("."+this._core.settings.itemClass),i=this._videos[t.attr("data-video")],n=i.width||"100%",o=i.height||this._core.$stage.height();this._playing||(this._core.enter("playing"),this._core.trigger("play",null,"video"),t=this._core.items(this._core.relative(t.index())),this._core.reset(t.index()),(e=h('<iframe frameborder="0" allowfullscreen mozallowfullscreen webkitAllowFullScreen ></iframe>')).attr("height",o),e.attr("width",n),"youtube"===i.type?e.attr("src","//www.youtube.com/embed/"+i.id+"?autoplay=1&rel=0&v="+i.id):"vimeo"===i.type?e.attr("src","//player.vimeo.com/video/"+i.id+"?autoplay=1"):"vzaar"===i.type&&e.attr("src","//view.vzaar.com/"+i.id+"/player?autoplay=true"),h(e).wrap('<div class="owl-video-frame" />').insertAfter(t.find(".owl-video")),this._playing=t.addClass("owl-video-playing"))},i.prototype.isInFullScreen=function(){var t=e.fullscreenElement||e.mozFullScreenElement||e.webkitFullscreenElement;return t&&h(t).parent().hasClass("owl-video-frame")},i.prototype.destroy=function(){var t,e;for(t in this._core.$element.off("click.owl.video"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},h.fn.owlCarousel.Constructor.Plugins.Video=i})(window.Zepto||window.jQuery,document),(r=>{function e(t){this.core=t,this.core.options=r.extend({},e.Defaults,this.core.options),this.swapping=!0,this.previous=void 0,this.next=void 0,this.handlers={"change.owl.carousel":r.proxy(function(t){t.namespace&&"position"==t.property.name&&(this.previous=this.core.current(),this.next=t.property.value)},this),"drag.owl.carousel dragged.owl.carousel translated.owl.carousel":r.proxy(function(t){t.namespace&&(this.swapping="translated"==t.type)},this),"translate.owl.carousel":r.proxy(function(t){t.namespace&&this.swapping&&(this.core.options.animateOut||this.core.options.animateIn)&&this.swap()},this)},this.core.$element.on(this.handlers)}e.Defaults={animateOut:!1,animateIn:!1},e.prototype.swap=function(){var t,e,i,n,o,s;1===this.core.settings.items&&r.support.animation&&r.support.transition&&(this.core.speed(0),e=r.proxy(this.clear,this),i=this.core.$stage.children().eq(this.previous),n=this.core.$stage.children().eq(this.next),o=this.core.settings.animateIn,s=this.core.settings.animateOut,this.core.current()!==this.previous)&&(s&&(t=this.core.coordinates(this.previous)-this.core.coordinates(this.next),i.one(r.support.animation.end,e).css({left:t+"px"}).addClass("animated owl-animated-out").addClass(s)),o)&&n.one(r.support.animation.end,e).addClass("animated owl-animated-in").addClass(o)},e.prototype.clear=function(t){r(t.target).css({left:""}).removeClass("animated owl-animated-out owl-animated-in").removeClass(this.core.settings.animateIn).removeClass(this.core.settings.animateOut),this.core.onTransitionEnd()},e.prototype.destroy=function(){var t,e;for(t in this.handlers)this.core.$element.off(t,this.handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},r.fn.owlCarousel.Constructor.Plugins.Animate=e})(window.Zepto||window.jQuery,document),((n,o,e)=>{function i(t){this._core=t,this._call=null,this._time=0,this._timeout=0,this._paused=!0,this._handlers={"changed.owl.carousel":n.proxy(function(t){t.namespace&&"settings"===t.property.name?this._core.settings.autoplay?this.play():this.stop():t.namespace&&"position"===t.property.name&&this._paused&&(this._time=0)},this),"initialized.owl.carousel":n.proxy(function(t){t.namespace&&this._core.settings.autoplay&&this.play()},this),"play.owl.autoplay":n.proxy(function(t,e,i){t.namespace&&this.play(e,i)},this),"stop.owl.autoplay":n.proxy(function(t){t.namespace&&this.stop()},this),"mouseover.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"mouseleave.owl.autoplay":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.play()},this),"touchstart.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this._core.is("rotating")&&this.pause()},this),"touchend.owl.core":n.proxy(function(){this._core.settings.autoplayHoverPause&&this.play()},this)},this._core.$element.on(this._handlers),this._core.options=n.extend({},i.Defaults,this._core.options)}i.Defaults={autoplay:!1,autoplayTimeout:5e3,autoplayHoverPause:!1,autoplaySpeed:!1},i.prototype._next=function(t){this._call=o.setTimeout(n.proxy(this._next,this,t),this._timeout*(Math.round(this.read()/this._timeout)+1)-this.read()),this._core.is("interacting")||e.hidden||this._core.next(t||this._core.settings.autoplaySpeed)},i.prototype.read=function(){return(new Date).getTime()-this._time},i.prototype.play=function(t,e){var i;this._core.is("rotating")||this._core.enter("rotating"),t=t||this._core.settings.autoplayTimeout,i=Math.min(this._time%(this._timeout||t),t),this._paused?(this._time=this.read(),this._paused=!1):o.clearTimeout(this._call),this._time+=this.read()%t-i,this._timeout=t,this._call=o.setTimeout(n.proxy(this._next,this,e),t-i)},i.prototype.stop=function(){this._core.is("rotating")&&(this._time=0,this._paused=!0,o.clearTimeout(this._call),this._core.leave("rotating"))},i.prototype.pause=function(){this._core.is("rotating")&&!this._paused&&(this._time=this.read(),this._paused=!0,o.clearTimeout(this._call))},i.prototype.destroy=function(){var t,e;for(t in this.stop(),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.autoplay=i})(window.Zepto||window.jQuery,window,document),(o=>{function e(t){this._core=t,this._initialized=!1,this._pages=[],this._controls={},this._templates=[],this.$element=this._core.$element,this._overrides={next:this._core.next,prev:this._core.prev,to:this._core.to},this._handlers={"prepared.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.push('<div class="'+this._core.settings.dotClass+'">'+o(t.content).find("[data-dot]").addBack("[data-dot]").attr("data-dot")+"</div>")},this),"added.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,0,this._templates.pop())},this),"remove.owl.carousel":o.proxy(function(t){t.namespace&&this._core.settings.dotsData&&this._templates.splice(t.position,1)},this),"changed.owl.carousel":o.proxy(function(t){t.namespace&&"position"==t.property.name&&this.draw()},this),"initialized.owl.carousel":o.proxy(function(t){t.namespace&&!this._initialized&&(this._core.trigger("initialize",null,"navigation"),this.initialize(),this.update(),this.draw(),this._initialized=!0,this._core.trigger("initialized",null,"navigation"))},this),"refreshed.owl.carousel":o.proxy(function(t){t.namespace&&this._initialized&&(this._core.trigger("refresh",null,"navigation"),this.update(),this.draw(),this._core.trigger("refreshed",null,"navigation"))},this)},this._core.options=o.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers)}e.Defaults={nav:!1,navText:['<span aria-label="Previous">&#x2039;</span>','<span aria-label="Next">&#x203a;</span>'],navSpeed:!1,navElement:'button type="button" role="presentation"',navContainer:!1,navContainerClass:"owl-nav",navClass:["owl-prev","owl-next"],slideBy:1,dotClass:"owl-dot",dotsClass:"owl-dots",dots:!0,dotsEach:!1,dotsData:!1,dotsSpeed:!1,dotsContainer:!1},e.prototype.initialize=function(){var t,i=this._core.settings;for(t in this._controls.$relative=(i.navContainer?o(i.navContainer):o("<div>").addClass(i.navContainerClass).appendTo(this.$element)).addClass("disabled"),this._controls.$previous=o("<"+i.navElement+">").addClass(i.navClass[0]).html(i.navText[0]).prependTo(this._controls.$relative).on("click",o.proxy(function(t){this.prev(i.navSpeed)},this)),this._controls.$next=o("<"+i.navElement+">").addClass(i.navClass[1]).html(i.navText[1]).appendTo(this._controls.$relative).on("click",o.proxy(function(t){this.next(i.navSpeed)},this)),i.dotsData||(this._templates=[o('<button role="button">').addClass(i.dotClass).append(o("<span>")).prop("outerHTML")]),this._controls.$absolute=(i.dotsContainer?o(i.dotsContainer):o("<div>").addClass(i.dotsClass).appendTo(this.$element)).addClass("disabled"),this._controls.$absolute.on("click","button",o.proxy(function(t){var e=(o(t.target).parent().is(this._controls.$absolute)?o(t.target):o(t.target).parent()).index();t.preventDefault(),this.to(e,i.dotsSpeed)},this)),this._overrides)this._core[t]=o.proxy(this[t],this)},e.prototype.destroy=function(){var t,e,i,n,o=this._core.settings;for(t in this._handlers)this.$element.off(t,this._handlers[t]);for(e in this._controls)"$relative"===e&&o.navContainer?this._controls[e].html(""):this._controls[e].remove();for(n in this.overides)this._core[n]=this._overrides[n];for(i in Object.getOwnPropertyNames(this))"function"!=typeof this[i]&&(this[i]=null)},e.prototype.update=function(){var t,e,i=this._core.clones().length/2,n=i+this._core.items().length,o=this._core.maximum(!0),s=this._core.settings,r=s.center||s.autoWidth||s.dotsData?1:s.dotsEach||s.items;if("page"!==s.slideBy&&(s.slideBy=Math.min(s.slideBy,s.items)),s.dots||"page"==s.slideBy)for(this._pages=[],t=i,e=0;t<n;t++){if(r<=e||0===e){if(this._pages.push({start:Math.min(o,t-i),end:t-i+r-1}),Math.min(o,t-i)===o)break;e=0,0}e+=this._core.mergers(this._core.relative(t))}},e.prototype.draw=function(){var t=this._core.settings,e=this._core.items().length<=t.items,i=this._core.relative(this._core.current()),n=t.loop||t.rewind;this._controls.$relative.toggleClass("disabled",!t.nav||e),t.nav&&(this._controls.$previous.toggleClass("disabled",!n&&i<=this._core.minimum(!0)),this._controls.$next.toggleClass("disabled",!n&&i>=this._core.maximum(!0))),this._controls.$absolute.toggleClass("disabled",!t.dots||e),t.dots&&(n=this._pages.length-this._controls.$absolute.children().length,t.dotsData&&0!=n?this._controls.$absolute.html(this._templates.join("")):0<n?this._controls.$absolute.append(new Array(1+n).join(this._templates[0])):n<0&&this._controls.$absolute.children().slice(n).remove(),this._controls.$absolute.find(".active").removeClass("active"),this._controls.$absolute.children().eq(o.inArray(this.current(),this._pages)).addClass("active"))},e.prototype.onTrigger=function(t){var e=this._core.settings;t.page={index:o.inArray(this.current(),this._pages),count:this._pages.length,size:e&&(e.center||e.autoWidth||e.dotsData?1:e.dotsEach||e.items)}},e.prototype.current=function(){var i=this._core.relative(this._core.current());return o.grep(this._pages,o.proxy(function(t,e){return t.start<=i&&t.end>=i},this)).pop()},e.prototype.getPosition=function(t){var e,i,n=this._core.settings;return"page"==n.slideBy?(e=o.inArray(this.current(),this._pages),i=this._pages.length,t?++e:--e,e=this._pages[(e%i+i)%i].start):(e=this._core.relative(this._core.current()),i=this._core.items().length,t?e+=n.slideBy:e-=n.slideBy),e},e.prototype.next=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!0),t)},e.prototype.prev=function(t){o.proxy(this._overrides.to,this._core)(this.getPosition(!1),t)},e.prototype.to=function(t,e,i){!i&&this._pages.length?(i=this._pages.length,o.proxy(this._overrides.to,this._core)(this._pages[(t%i+i)%i].start,e)):o.proxy(this._overrides.to,this._core)(t,e)},o.fn.owlCarousel.Constructor.Plugins.Navigation=e})(window.Zepto||window.jQuery,document),((n,o)=>{function e(t){this._core=t,this._hashes={},this.$element=this._core.$element,this._handlers={"initialized.owl.carousel":n.proxy(function(t){t.namespace&&"URLHash"===this._core.settings.startPosition&&n(o).trigger("hashchange.owl.navigation")},this),"prepared.owl.carousel":n.proxy(function(t){var e;t.namespace&&(e=n(t.content).find("[data-hash]").addBack("[data-hash]").attr("data-hash"))&&(this._hashes[e]=t.content)},this),"changed.owl.carousel":n.proxy(function(t){var i;t.namespace&&"position"===t.property.name&&(i=this._core.items(this._core.relative(this._core.current())),t=n.map(this._hashes,function(t,e){return t===i?e:null}).join())&&o.location.hash.slice(1)!==t&&(o.location.hash=t)},this)},this._core.options=n.extend({},e.Defaults,this._core.options),this.$element.on(this._handlers),n(o).on("hashchange.owl.navigation",n.proxy(function(t){var e=o.location.hash.substring(1),i=this._core.$stage.children(),i=this._hashes[e]&&i.index(this._hashes[e]);void 0!==i&&i!==this._core.current()&&this._core.to(this._core.relative(i),!1,!0)},this))}e.Defaults={URLhashListener:!1},e.prototype.destroy=function(){var t,e;for(t in n(o).off("hashchange.owl.navigation"),this._handlers)this._core.$element.off(t,this._handlers[t]);for(e in Object.getOwnPropertyNames(this))"function"!=typeof this[e]&&(this[e]=null)},n.fn.owlCarousel.Constructor.Plugins.Hash=e})(window.Zepto||window.jQuery,window,document),(o=>{function e(t,i){var n=!1,e=t.charAt(0).toUpperCase()+t.slice(1);return o.each((t+" "+r.join(e+" ")+e).split(" "),function(t,e){if(void 0!==s[e])return n=!i||e,!1}),n}function t(t){return e(t,!0)}var s=o("<support>").get(0).style,r="Webkit Moz O ms".split(" "),i={transition:{end:{WebkitTransition:"webkitTransitionEnd",MozTransition:"transitionend",OTransition:"oTransitionEnd",transition:"transitionend"}},animation:{end:{WebkitAnimation:"webkitAnimationEnd",MozAnimation:"animationend",OAnimation:"oAnimationEnd",animation:"animationend"}}},n=function(){return!!e("transform")},a=function(){return!!e("perspective")},l=function(){return!!e("animation")};!function(){return!!e("transition")}()||(o.support.transition=new String(t("transition")),o.support.transition.end=i.transition.end[o.support.transition]),l()&&(o.support.animation=new String(t("animation")),o.support.animation.end=i.animation.end[o.support.animation]),n()&&(o.support.transform=new String(t("transform")),o.support.transform3d=a())})(window.Zepto||window.jQuery,document),jQuery.noConflict(),(()=>{var t,e=jQuery(".jarallax-img");e&&(t={disableParallax:/iPad|iPhone|iPod|Android/,disableVideo:/iPad|iPhone|iPod|Android/,speed:.1},e.imagesLoaded(function(){e.parent().jarallax(t).addClass("initialized")}))})(),jQuery(".featured-posts").owlCarousel({dots:!1,margin:30,nav:!0,navText:['<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M20.547 22.107L14.44 16l6.107-6.12L18.667 8l-8 8 8 8 1.88-1.893z"></path></svg>','<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="18" height="18" fill="currentColor"><path d="M11.453 22.107L17.56 16l-6.107-6.12L13.333 8l8 8-8 8-1.88-1.893z"></path></svg>'],responsive:{0:{items:1,slideBy:1},768:{items:3,slideBy:3},992:{items:4,slideBy:4}}}),document.addEventListener("mousemove",function(t){var e=t.clientX/window.innerWidth;document.body.style.backgroundPosition=50+100*(e-.5)+`% ${50+100*(t.clientY/window.innerHeight-.5)}%`});