{"version": 3, "sources": ["../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/vars.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/reset.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/global.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/layout.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/header.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/cover.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/loop.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/post-card.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/article.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/content.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/comments.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/cta.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/card.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/pagination.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/navigation.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/button.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/components/footer.css", "../../node_modules/@tryghost/shared-theme-assets/assets/css/v1/vendor/pswp.css", "general/vars.css", "general/form.css", "site/layout.css", "site/header.css", "site/cover.css", "site/search.css", "site/page-search.css", "site/topic.css", "site/term.css", "blog/post.css", "blog/feed.css", "blog/pagination.css", "blog/featured.css", "blog/article.css", "blog/related.css", "blog/comment.css", "misc/utilities.css", "misc/owl.css", "general/styles.css"], "names": [], "mappings": "AAAA,MACI,yBAA0B,CAC1B,2BAA4B,CAC5B,kBAAmB,CACnB,4BAA6B,CAC7B,0BAA2B,CAC3B,qBAAsB,CACtB,sBAAuB,CACvB,2BAA4B,CAC5B,kBAAmB,CACnB,gFAAsF,CACtF,0BAA4B,CAC5B,0EAAgF,CAChF,qBAAsB,CACtB,gBAAiB,CACjB,YAAa,CACb,qBACJ,CAEA,yBACI,MACI,0BAA4B,CAC5B,UAAW,CACX,qBACJ,CACJ,CCxBA,iBAGI,qBACJ,CAGA,iDAYI,QACJ,CAGA,4BAEI,eACJ,CAGA,kBACI,sBACJ,CAGA,KAEI,eAAgB,CADhB,gBAAiB,CAEjB,4BACJ,CAGA,eACI,gCAA8B,CAA9B,6BACJ,CAGA,YAEI,aAAc,CACd,cACJ,CAGA,6BAII,YACJ,CAGA,uCACI,kBACI,oBACJ,CAEA,iBAII,kCAAqC,CACrC,qCAAuC,CACvC,8BAAgC,CAHhC,mCAIJ,CACJ,CC3EA,KACI,eACJ,CAEA,KAKI,mCAAoC,CADpC,+BAAgC,CAHhC,gDAAkD,CAClD,gBAAiB,CACjB,eAAgB,CAGhB,kCAAmC,CACnC,iCACJ,CAEA,IACI,WACJ,CAEA,EACI,8BAA+B,CAC/B,oBACJ,CAEA,QACI,UACJ,CAEA,kBAQI,8BAA+B,CAF/B,mDAAqD,CAGrD,qBAAuB,CAFvB,gBAGJ,CAEA,GACI,wBACJ,CAEA,GACI,gBACJ,CAEA,GACI,gBACJ,CAEA,GACI,gBACJ,CAEA,GACI,cACJ,CAEA,GACI,gBACJ,CAEA,GAGI,wCAAyC,CACzC,QAAS,CAFT,UAAW,CADX,UAIJ,CAEA,wBAEI,+CAAgD,CADhD,iBAEJ,CAEA,WAII,iCAAkC,CAFlC,gBAAiB,CACjB,eAAgB,CAFhB,iBAAkB,CAIlB,iBACJ,CAEA,0BACI,gBACJ,CAEA,yBACI,8BAA+B,CAC/B,oBACJ,CAEA,IAMI,0CAA2C,CAH3C,oBAAa,CAAb,YAAa,CACb,eAAgB,CAFhB,iBAAkB,CADlB,qBAAsB,CAItB,eAAgB,CAEhB,gCACJ,CAEA,KACI,4BAA6B,CAC7B,cACJ,CAEA,eAGI,0CAA2C,CAC3C,iBAAkB,CAFlB,+BAAgC,CADhC,aAIJ,CAEA,OAII,QAAS,CAHT,aAAc,CAEd,eAAgB,CADhB,UAGJ,CAEA,yBACI,GACI,gBACJ,CAEA,GACI,gBACJ,CACJ,CCjIA,SACI,YAAa,CACb,qBAAsB,CACtB,gBACJ,CAEA,SACI,WAAY,CAEZ,mBAAoB,CADpB,gBAEJ,CAEA,wBACI,eACJ,CAEA,UAEI,uBAAwB,CADxB,wBAEJ,CAEA,UAEI,aAAc,CADd,uCAEJ,CAMA,0CAEI,4DAA+D,CAC/D,uFAA2F,CAC3F,6BAA+B,CAE/B,YAAa,CACb,yJAMJ,CAEA,aACI,gBACJ,CAEA,oCAEI,gBACJ,CAEA,eACI,gBACJ,CAEA,mBACI,UACJ,CAEA,yBACI,SAEI,qBAAsB,CADtB,kBAEJ,CACJ,CCnEA,SAEI,mCAAoC,CADpC,YAEJ,CAEA,eAKI,kBAAmB,CADnB,mCAA+B,CAA/B,8BAA+B,CAH/B,YAAa,CAEb,wBAAyB,CADzB,mCAAoC,CAIpC,WACJ,CAEA,eACI,aACJ,CAEA,uBAEI,kBAAmB,CADnB,YAEJ,CAEA,cAEI,mDAAqD,CACrD,gBAAiB,CACjB,eAAgB,CAChB,qBAAuB,CAJvB,iBAKJ,CAEA,kBACI,eACJ,CAEA,+BAGI,MAAO,CACP,SAAU,CAHV,iBAAkB,CAClB,KAGJ,CAEA,cACI,YAGJ,CAEA,4BAHI,kBAAmB,CADnB,uBAYJ,CARA,cACI,mBAAoB,CAMpB,eAAgB,CAFhB,QAAS,CADT,SAAU,CAEV,kBAEJ,CAEA,0BAGI,iBAAkB,CADlB,aAAc,CADd,iBAAkB,CAGlB,sBACJ,CAEA,8BAEI,WAAY,CADZ,UAEJ,CAEA,iBAGI,kBAAmB,CAFnB,YAAa,CACb,uBAAwB,CAExB,wBACJ,CAEA,iBAGI,kBAAmB,CAFnB,YAAa,CACb,QAAS,CAET,kBACJ,CAEA,oBACI,iBAAkB,CAClB,eACJ,CAEA,0BAEI,kBAAmB,CACnB,+BAAgC,CAFhC,mBAGJ,CAEA,iBAEI,YAAa,CADb,WAEJ,CAEA,WAEI,gBAAiB,CADjB,iBAEJ,CAEA,iBACI,UACJ,CAEA,0BACI,gBACJ,CAEA,yBACI,iBACI,6BAA8B,CAC9B,QAAS,CACT,UACJ,CAEA,4BACI,YACJ,CACJ,CAEA,yBACI,0BACI,YACJ,CAEA,oDACI,SACJ,CACJ,CAWA,kCACI,mCACJ,CAEA,yBACI,iCAEI,gBAAiB,CADjB,iBAEJ,CACJ,CAQA,oCACI,kCACJ,CAEA,oCACI,mBACJ,CAEA,sCACI,QACJ,CAEA,yBACI,mCACI,iBACJ,CACJ,CASA,0BAEI,WAAY,CADZ,iBAEJ,CAEA,gCACI,kCACJ,CAEA,gCACI,YAAa,CAEb,mBAAoB,CADpB,gBAAiB,CAEjB,eACJ,CAEA,yBACI,gCACI,gBACJ,CACJ,CAEA,yBACI,gCACI,SACJ,CAEA,gCAEI,kBAAmB,CADnB,YAAa,CAEb,WACJ,CAEA,+BAEI,eAAkB,CADlB,gBAAiB,CAGjB,WAAY,CADZ,sBAAuB,CAEvB,aACJ,CAEA,2EAQI,wCAAyC,CADzC,UAAW,CADX,UAAW,CAFX,MAAO,CAFP,iBAAkB,CAClB,QAAS,CAET,UAIJ,CAEA,qCACI,SACJ,CAEA,kCAEI,eAAkB,CADlB,gBAAiB,CAEjB,6BACJ,CACJ,CAKA,8BAKI,4BAA6B,CAF7B,MAAO,CAFP,iBAAkB,CAClB,OAAQ,CAER,UAEJ,CAEA,mSAQI,wBACJ,CAEA,6EAEI,mCACJ,CAEA,kCAEI,qBAAsB,CADtB,aAEJ,CAEA,mFAEI,mCACJ,CAEA,4CAGI,MAAO,CAFP,iBAAkB,CAClB,OAEJ,CAEA,qFACI,eACJ,CAKA,iDACI,yCACJ,CAEA,wbAQI,wBACJ,CAEA,mHAEI,mCACJ,CAEA,qDAEI,qBAAsB,CADtB,aAEJ,CAEA,yHAEI,mCACJ,CAKA,kDACI,0CACJ,CAEA,gcAQI,wBACJ,CAEA,qHAEI,mCACJ,CAEA,sDAEI,qBAAsB,CADtB,aAEJ,CAEA,2HAEI,mCACJ,CAKA,aAUI,qBAAsB,CACtB,iBAAkB,CAClB,oEAA8E,CAL9E,eAAgB,CAMhB,SAAU,CAPV,cAAe,CALf,iBAAkB,CAElB,WAAY,CAKZ,eAAgB,CANhB,QAAS,CAaT,8BAAiC,CADjC,oCAAwC,CALxC,iBAAkB,CAJlB,WAAY,CADZ,UAYJ,CAEA,kCAEI,UAAW,CADX,UAEJ,CAEA,+BAII,oBAAgB,CAAhB,eAAgB,CAHhB,YAAa,CAEb,qBAAsB,CADtB,6BAA8B,CAI9B,iBAAkB,CADlB,UAEJ,CAEA,+BAEI,SAAU,CACV,uBAAwB,CAFxB,kBAGJ,CAEA,kBAGI,uBAAyB,CAFzB,aAAc,CACd,gBAEJ,CAEA,oCACI,aACJ,CAKA,WAOI,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAEhB,4BAA6B,CAC7B,QAAS,CAFT,cAAe,CANf,YAAa,CAEb,WAAY,CAEZ,iBAAkB,CADlB,SAAU,CAJV,iBAAkB,CAElB,UAQJ,CAEA,mCAOI,yCAA0C,CAD1C,UAAW,CADX,UAAW,CAFX,QAAS,CADT,iBAAkB,CAMlB,uDAAiE,CAJjE,UAKJ,CAEA,kBACI,QACJ,CAEA,iBACI,WACJ,CAEA,gCACI,QAAS,CACT,uBACJ,CAEA,+BACI,WAAY,CACZ,wBACJ,CAEA,yBACI,SACI,WACJ,CAEA,wBAGI,QAAS,CADT,yBAA0B,CAD1B,gCAGJ,CAEA,wBAII,kBAAmB,CAHnB,YAAa,CAEb,mBAAoB,CADpB,mCAAoC,CAGpC,WACJ,CAEA,uBACI,gBACJ,CAEA,WACI,aACJ,CAEA,iDAGI,sBAAuB,CAEvB,SAAU,CAHV,cAAe,CAEf,iBAEJ,CAEA,uBAEI,uBAAwB,CADxB,eAEJ,CAEA,cAEI,kBAAmB,CADnB,QAAS,CAET,eACJ,CAEA,gBACI,gBAAiB,CACjB,eAAgB,CAChB,mBACJ,CAEA,iBACI,SAAU,CACV,0BACJ,CAEA,0BACI,iBACJ,CAEA,yCACI,SAAU,CACV,yBACJ,CAEA,sBAEI,gBAAiB,CACjB,mBAAoB,CAFpB,UAGJ,CAEA,mCACI,cACJ,CAEA,SACI,sBACJ,CAEA,uBAII,WAAY,CAFZ,OAAQ,CAGR,iBAAkB,CAJlB,cAAe,CAEf,eAAgB,CAGhB,gCACJ,CAEA,wFACI,mCACJ,CAEA,2HAEI,0CACJ,CAEA,yFAEI,yCACJ,CAEA,6EAII,SAAU,CAFV,eAAgB,CAChB,kBAEJ,CAEA,4BACI,YAAa,CACb,qBACJ,CAEA,+BACI,SAAU,CAEV,uBAAwB,CADxB,oCAEJ,CAEA,wCAQI,kBAAmB,CAEnB,mCAAoC,CAPpC,QAAS,CAET,mBAAoB,CACpB,qBAAsB,CACtB,QAAS,CAHT,MAAO,CAKP,2CAA4C,CAR5C,eAAgB,CAChB,OASJ,CAEA,uDACI,SAAU,CAGV,uBAAwB,CAFxB,oCAAwC,CACxC,oBAEJ,CAEA,qCACI,oBACJ,CAEA,uBACI,SACJ,CACJ,CC9lBA,UAEI,mCAAqC,CAErC,qBAAsB,CADtB,kBAAmB,CAFnB,iBAIJ,CAEA,gBAMI,WAAY,CAJZ,OAAQ,CAER,mBAAiB,CAAjB,gBAAiB,CAHjB,iBAAkB,CAIlB,UAAW,CAFX,UAIJ,CAEA,gGACI,oDACJ,CAEA,+FACI,iDACJ,CCtBA,+FACI,oDAAsD,CACtD,qBACJ,CAEA,0FACI,iDACJ,CCPA,SACI,qBACJ,CCFA,kCACI,qCACJ,CAEA,gBACI,+BACJ,CAEA,kBACI,qBACJ,CAEA,kGACI,oDAAsD,CACtD,qBACJ,CAEA,oBAII,iCAAkC,CAFlC,gBAAiB,CACjB,eAAgB,CAFhB,eAIJ,CAEA,6FACI,iDACJ,CAEA,kBACI,+BAAgC,CAChC,eACJ,CAEA,sBACI,UACJ,CAEA,yBACI,oBACI,gBACJ,CAEA,kBACI,iBACJ,CACJ,CCzCA,YAEI,yCAA2C,CAC3C,8CAAgD,CAFhD,eAAgB,CAGhB,qBACJ,CAGA,gBAEI,eAAgB,CADhB,2DAEJ,CAGA,iBACI,QACJ,CAEA,0BACI,mEACJ,CAEA,iGACI,oDAAsD,CACtD,qBACJ,CAIA,mCACI,2DACJ,CAGA,mBACI,0DACJ,CAGA,sCAEI,iBAEJ,CAEA,gFAHI,2DAMJ,CAGA,eACI,eACJ,CAEA,eACI,eACJ,CAEA,cACI,+BAAgC,CAChC,yBAA0B,CAC1B,qBACJ,CAEA,mMAII,eACJ,CAEA,q2BASI,iDACJ,CAEA,6CAGI,mBACJ,CAEA,qCACI,gBACJ,CAEA,qBACI,2BACJ,CAEA,wBACI,2BACJ,CAEA,mCAUI,wBAAyB,CADzB,gBAAiB,CARjB,oBAAqB,CAIrB,4BAA6B,CAC7B,gBAAiB,CAHjB,cAAe,CACf,eAAgB,CAIhB,kBAAmB,CADnB,kBAAmB,CALnB,UAAW,CASX,gCAAiC,CACjC,sJAA8L,CAE9L,mCAAqC,CADrC,2BAA4B,CAE5B,mCACJ,CAEA,kDACI,kEAAgG,CAChG,2BAA4B,CAC5B,yBACJ,CAEA,iDACI,mEAA+F,CAE/F,0BAA2B,CAD3B,2BAA4B,CAE5B,yBACJ,CAEA,sCAOI,mCAAoC,CAJpC,2BAA4B,CAF5B,gBAAiB,CACjB,eAAgB,CAIhB,mBAAqB,CAFrB,eAAgB,CAChB,wBAGJ,CAEA,4EAGI,wCAAyC,CADzC,gBAEJ,CAGA,sCACI,sBACJ,CAEA,uEACI,qCACJ,CAEA,2DACI,qCACJ,CAEA,yBACI,YAEI,gBAAiB,CADjB,iBAEJ,CACJ,CC3KA,aAEI,mBAAoB,CADpB,eAEJ,CAEA,oBAEI,kBAAmB,CADnB,YAAa,CAEb,6BAA8B,CAC9B,kBACJ,CAEA,gCACI,kBACJ,CAEA,uCACI,eACJ,CAEA,mBACI,iCACJ,CCtBA,iBAOI,uEAA+E,CAD/E,UAAW,CAJX,+BAAkC,CAClC,YAAa,CAEb,kBAAmB,CADnB,iBAAkB,CAHlB,iBAOJ,CAEA,6BACI,YACJ,CAEA,QACI,YAAa,CACb,qBAAsB,CACtB,kBAAmB,CACnB,iBACJ,CAEA,cAEI,gBAAiB,CACjB,qBAAuB,CAFvB,oBAGJ,CAEA,gBAGI,kBAAmB,CAFnB,YAAa,CACb,qBAEJ,CAEA,aAGI,iCAAkC,CAElC,cAAe,CAHf,gBAAiB,CADjB,gBAAkB,CAGlB,oBAEJ,CAEA,mBACI,8BACJ,CAEA,yBACI,gBACI,qBAAkB,CAAlB,gBACJ,CACJ,CC9CA,oEACI,yDACJ,CAEA,oEACI,yDACJ,CAGA,gGACI,YACJ,CAKA,UAEI,gBAAiB,CADjB,iBAEJ,CAKA,eAGI,kBAAmB,CAFnB,YAAa,CACb,qBAAsB,CAEtB,UACJ,CAKA,2CAEI,cACJ,CAEA,iDAEI,mBACJ,CAKA,gDACI,cAAe,CACf,eACJ,CAEA,wHACI,oDACJ,CAKA,yBACI,mBACJ,CAEA,0BACI,yBACJ,CAKA,6BAGI,iCAAkC,CAFlC,iBAAkB,CAClB,eAEJ,CAKA,gCACI,aACJ,CAKA,iIACI,oDACJ,CAEA,qHACI,iDACJ,CAKA,0LAMI,iDAAoD,CACpD,6CAAgD,CAChD,SACJ,CAEA,yCACI,6BACJ,CAEA,uCACI,cACJ,CAEA,qCACI,cACJ,CAEA,2CAGI,cAAgB,CADhB,eAAiB,CADjB,eAGJ,CAEA,wCACI,cACJ,CAEA,6CACI,2BACJ,CAKA,sCACI,UACJ,CAEA,uDACI,wBACJ,CClJA,YACI,YAAa,CACb,kCAAmC,CACnC,iBACJ,CAEA,aACI,mBACJ,CAEA,aACI,gBACJ,CCZA,eAII,kBAAmB,CADnB,sBAAkB,CAAlB,iBAAkB,CAFlB,YAAa,CACb,kCAGJ,CAEA,mBAEI,kBAAmB,CADnB,YAEJ,CAEA,oBACI,wBACJ,CAEA,oBAEI,kBAAmB,CADnB,mBAEJ,CAEA,wBAEI,WAAY,CADZ,UAEJ,CAEA,4BACI,kBACJ,CAEA,wBACI,iBACJ,CAEA,yBACI,oBACI,YACJ,CACJ,CCtCA,QAGI,kBAAmB,CASnB,yCAA0C,CAC1C,QAAS,CACT,mBAAoB,CALpB,wBAAyB,CAEzB,cAAe,CAVf,mBAAoB,CAKpB,gBAAiB,CACjB,eAAgB,CALhB,QAAU,CAEV,sBAAuB,CAMvB,sBAAuB,CAFvB,aAAc,CAHd,0EAUJ,CAEA,cACI,WACJ,CAEA,6BACI,mBACJ,CAEA,gBACI,0CACJ,CAEA,gBAGI,4BAA6B,CAC7B,wCAAyC,CAHzC,8BAA+B,CAC/B,oBAGJ,CAEA,sBACI,kCAAmC,CACnC,SACJ,CAEA,aAEI,kBAAmB,CAOnB,4BAA6B,CAC7B,QAAS,CAHT,8BAA+B,CAC/B,cAAe,CAPf,mBAAoB,CAIpB,WAAY,CAFZ,sBAAuB,CAQvB,YAAa,CALb,SAAU,CAFV,UAQJ,CAEA,iBAEI,WAAY,CADZ,UAEJ,CAEA,aAEI,kBAAmB,CAOnB,4BAA6B,CAC7B,QAAS,CAJT,8BAA+B,CAE/B,cAAe,CAPf,mBAAoB,CAEpB,sBAAuB,CAIvB,sBAAuB,CAFvB,QAAS,CAMT,YAAa,CAPb,SAAU,CAQV,mBACJ,CAEA,mBACI,UACJ,CC7EA,SAGI,iCAAkC,CADlC,mBAAoB,CADpB,gBAAiB,CAGjB,kBACJ,CAEA,eACI,YAAa,CAGb,gBAAiB,CADjB,QAAS,CADT,kCAGJ,CAEA,wBAII,kBAAmB,CAHnB,YAAa,CACb,qBAAsB,CACtB,QAEJ,CAEA,mBAII,kBAAmB,CAHnB,mBAAoB,CACpB,cAAe,CAGf,sBAAuB,CAGvB,eAAgB,CADhB,QAAS,CADT,SAAU,CAHV,WAMJ,CAEA,sBAEI,kBAAmB,CADnB,YAEJ,CAEA,gCAII,WAAY,CAFZ,eAAiB,CACjB,aAAc,CAFd,gBAIJ,CAEA,eACI,gBACJ,CAEA,WACI,iCACJ,CAEA,iBACI,8BAA+B,CAC/B,SACJ,CAEA,yBACI,SAEI,oBAAqB,CADrB,kBAEJ,CAEA,eACI,yBAA0B,CAC1B,iBACJ,CAEA,mBACI,qBACJ,CAEA,yBACI,gBACJ,CAEA,gCACI,YACJ,CAEA,eACI,iBACJ,CACJ,CCnFA,MAYI,0BAA2B,CAP3B,YAAa,CAEb,WAAY,CAJZ,MAAO,CAQP,YAAa,CAHb,eAAgB,CAPhB,iBAAkB,CAClB,KAAM,CAQN,iBAAkB,CAJlB,UAAW,CAFX,eAAgB,CAShB,6BACJ,CAEA,UACI,cACJ,CAEA,uBACI,YAAc,CACd,iDAAuD,CACvD,mBACJ,CAEA,YACI,aACJ,CAEA,+BACI,cACJ,CAEA,4BACI,WACJ,CAEA,2BACI,eACJ,CAEA,UAUI,0BAA2B,CAJ3B,gCAAqC,CACrC,SAAU,CAEV,uBAAwB,CADxB,iDAAuD,CAGvD,mBACJ,CAEA,6BATI,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAiBJ,CAPA,mBAMI,eACJ,CAEA,kCAQI,0BAA2B,CAH3B,QAAS,CACT,MAAO,CAJP,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAIN,iBAEJ,CAEA,4BAEI,wBAAiB,CAAjB,qBAAiB,CAAjB,gBAAiB,CACjB,uCAAwC,CACxC,0BACJ,CAEA,iBACI,iBAAkB,CAGlB,yBAA0B,CAD1B,mDAAyD,CADzD,UAGJ,CAEA,iEAEI,eACJ,CAEA,YAII,QAAS,CAET,eAAgB,CAHhB,OAIJ,CAEA,uBAJI,MAAO,CAJP,iBAAkB,CAClB,KAaJ,CANA,WAKI,WAAY,CADZ,UAEJ,CAEA,wBACI,0BACJ,CAEA,+BACI,6BACJ,CAEA,qBAII,qBAAuB,CAFvB,MAAO,CADP,KAAM,CAEN,oBAEJ,CAEA,iBAQI,iCAAkC,CAFlC,cAAe,CAHf,MAAO,CAIP,gBAAiB,CAFjB,eAAgB,CAJhB,iBAAkB,CAQlB,iBAAkB,CAPlB,OAAQ,CAER,UAMJ,CAEA,mBACI,iCAAkC,CAClC,yBACJ,CAEA,cASI,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAEhB,eAAgB,CAChB,QAAS,CACT,eAAgB,CAHhB,cAAe,CARf,aAAc,CACd,WAAY,CAEZ,WAAY,CAEZ,QAAS,CACT,gBAAiB,CAFjB,SAAU,CALV,iBAAkB,CAalB,sBAAwB,CAVxB,UAWJ,CAEA,wCAEI,SACJ,CAEA,qBAEI,UAAY,CADZ,YAEJ,CAEA,gCAEI,QAAS,CADT,SAEJ,CAEA,2CACI,SACJ,CAEA,mFAKI,wDAA2D,CAC3D,0BAA2B,CAF3B,WAAY,CADZ,UAIJ,CAEA,wDACI,oHAGI,gDACJ,CAEA,6EAEI,eACJ,CACJ,CAEA,qBACI,2BACJ,CAEA,qBACI,+BACJ,CAEA,kBACI,YACJ,CAEA,qCACI,aACJ,CAEA,4BACI,2BACJ,CAEA,oBAEI,2BAA4B,CAD5B,YAEJ,CAEA,wCACI,aACJ,CAEA,qCACI,4BACJ,CAEA,iFAEI,iBACJ,CAEA,uDAOI,eAAgB,CAFhB,YAAa,CACb,gBAAiB,CAJjB,iBAAkB,CAClB,OAAQ,CACR,UAIJ,CAEA,2BACI,MACJ,CAEA,4BACI,OACJ,CAEA,qEAMI,UAAW,CADX,WAAY,CAHZ,iBAAkB,CAClB,QAAS,CACT,UAGJ,CAEA,kCAEI,gCAAiC,CADjC,QAEJ,CAEA,mCAEI,+BAAgC,CADhC,SAEJ,CAEA,eASI,wBAAyB,CAHzB,cAAe,CACf,eAAgB,CAHhB,WAAY,CADZ,MAAO,CAKP,gBAAiB,CAHjB,cAAe,CAJf,iBAAkB,CAClB,KAAM,CAQN,wBAAiB,CAAjB,qBAAiB,CAAjB,gBACJ,CAEA,eAEI,QAAS,CACT,MAAO,CAEP,eAAgB,CAJhB,iBAAkB,CAGlB,UAEJ,CAEA,uBAMI,wBAAyB,CAFzB,cAAe,CACf,eAAgB,CAFhB,aAAc,CAFd,eAAgB,CAChB,sBAAuB,CAKvB,iBACJ,CAEA,2CAEI,cAAe,CACf,eAAgB,CAFhB,iBAAkB,CAGlB,wBACJ,CAEA,8EAEI,gBAAiB,CADjB,aAEJ,CAEA,sBACI,YACJ,CAEA,qBACI,iBACJ,CAEA,iBASI,aAAc,CAJd,WAAY,CAFZ,QAAS,CAGT,iBAAkB,CAClB,SAAU,CANV,iBAAkB,CAClB,KAAM,CAMN,gCAAkC,CAJlC,UAAW,CAMX,mBACJ,CAEA,sBAEI,WAAY,CACZ,WAAY,CAFZ,UAGJ,CAEA,yBACI,SACJ,CAEA,+CACI,qDACJ,CAEA,8CACI,SACJ,CAEA,oEACI,uCACJ,CAEA,sEACI,2DACJ,CAEA,2CAOI,eAAgB,CAFhB,WAAY,CAFZ,SAAU,CAGV,QAAS,CAET,WAAa,CAPb,iBAAkB,CAClB,QAAS,CAET,UAKJ,CAEA,2CAGI,WAAY,CACZ,eAAgB,CAHhB,iBAAkB,CAClB,SAGJ,CAEA,6CAQI,eAAgB,CAEhB,mCAAgC,CAChC,iCAA8B,CAC9B,iBAAkB,CAHlB,yCAAoC,CAApC,uCAAoC,CALpC,qBAAsB,CAEtB,WAAY,CAHZ,MAAO,CAIP,QAAS,CANT,iBAAkB,CAClB,KAAM,CAGN,UAQJ,CAEA,qCACI,iBAII,WAAY,CADZ,SAAU,CAEV,QAAS,CAJT,iBAAkB,CAClB,QAIJ,CACJ,CAEA,qBACI,GACI,sBACJ,CAEA,GACI,uBACJ,CACJ,CAEA,wBACI,GACI,mBACJ,CAEA,IACI,yBACJ,CAEA,GACI,mBACJ,CACJ,CAEA,UAGI,SAAU,CADV,kBAAmB,CADnB,YAAa,CAGb,2BACJ,CAEA,eAKI,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAEJ,CAEA,uHAII,0BAA2B,CAC3B,iDAAuD,CACvD,mBACJ,CAEA,yFAEI,kBACJ,CAMA,sHAEI,SACJ,CAEA,6JAII,YACJ,CAEA,qIAGI,YACJ,CAEA,yBACI,sBACJ,CAEA,oCACI,eACJ,CC3eA,MACI,iDAAmD,CACnD,yBAA0B,CAC1B,2BAA4B,CAC5B,mBAAoB,CACpB,sBAAuB,CACvB,qBAAsB,CACtB,kBAAmB,CACnB,4BAA6B,CAC7B,0BAA2B,CAC3B,wBAAyB,CACzB,yBAA0B,CAC1B,kBAAmB,CACnB,4BAA6B,CAC7B,gBAAiB,CACjB,qBAAsB,CACtB,kBACJ,CCjBA,2EAQI,uBAAgB,CAAhB,oBAAgB,CAAhB,eAAgB,CAChB,sCAAuC,CACvC,iBAAkB,CAHlB,cAAe,CAFf,WAAY,CAMZ,YAAa,CALb,cAAe,CAFf,UAQJ,CAEA,mGAII,iCACJ,CAEA,cACI,iBACJ,CAEA,aAKI,kBAAmB,CAQnB,qCAAsC,CACtC,QAAS,CACT,iBAAkB,CAJlB,wBAAyB,CACzB,cAAe,CARf,YAAa,CAMb,cAAe,CAFf,WAAY,CAFZ,sBAAuB,CAUvB,YAAa,CAPb,SAAU,CARV,iBAAkB,CAElB,SAAU,CADV,OAAQ,CAKR,UAUJ,CC1CA,MACI,YAAa,CACb,qBAAsB,CACtB,gBACJ,CAEA,cACI,WAAY,CAEZ,mBAAoB,CADpB,gBAEJ,CAEA,6BACI,eACJ,CAEA,yBACI,cACI,gBACJ,CACJ,CCpBA,cACI,qBACJ,CAEA,4BAEI,gBAAiB,CACjB,eACJ,CAEA,oBACI,YACJ,CCZA,YAEI,YAAa,CACb,gBAAiB,CAFjB,uBAGJ,CAEA,oBAEI,kBAAmB,CADnB,WAEJ,CAEA,0BAEI,kBAAmB,CAMnB,mCAAoC,CACpC,iBAAkB,CAHlB,aAAe,CACf,cAAe,CANf,YAAa,CAIb,cAAe,CAFf,WAAY,CACZ,cAMJ,CAEA,eAKI,wBAAyB,CADzB,WAAY,CADZ,YAAa,CAFb,iBAAkB,CAKlB,iBAAkB,CAJlB,UAKJ,CAEA,mBAGI,mDAAqD,CACrD,gBAAiB,CACjB,eAAgB,CAChB,eAAgB,CAJhB,aAAc,CADd,eAAgB,CAMhB,qBACJ,CAEA,mGACI,oDAAsD,CACtD,eACJ,CAEA,yBACI,oBACI,UACJ,CACJ,CAEA,yBACI,YACI,gBACJ,CAEA,mBACI,cACJ,CACJ,CC7DA,QACI,iBAAkB,CAClB,eACJ,CAEA,gCACI,YACJ,CAEA,qBACI,cACJ,CAEA,qCACI,YACJ,CAEA,sCACI,aACJ,CAEA,eAUI,mCAAoC,CACpC,iBAAkB,CAClB,kCAAuC,CAHvC,4BAA6B,CAD7B,cAAe,CAHf,cAAe,CADf,eAAgB,CAEhB,eAAgB,CAChB,iBAAkB,CANlB,iBAAkB,CAClB,QAAS,CACT,UAAW,CAUX,gCACJ,CAEA,sCACI,0CACJ,CAEA,wBACI,aAAc,CACd,sBACJ,CAEA,8BAEI,wCAAyC,CADzC,4BAA6B,CAE7B,SACJ,CAEA,yBACI,eAAgB,CAChB,gBACJ,CAEA,2BAKI,iCAAkC,CAJlC,aAAc,CAGd,gBAAiB,CAFjB,cAAe,CACf,eAAgB,CAGhB,sBAAuB,CACvB,kBACJ,CC3DA,mBAOI,yBAA0B,CAC1B,iCAAkC,CAHlC,WAAY,CAFZ,MAAO,CAFP,cAAe,CACf,KAAM,CAEN,UAAW,CAEX,YAGJ,CAEA,4BAMI,yBAA8B,CAD9B,WAAY,CAFZ,MAAO,CAFP,iBAAkB,CAClB,KAAM,CAEN,UAGJ,CAEA,2BAII,6BAA8B,CAC9B,iBAAkB,CAClB,qCAA0C,CAH1C,kBAAmB,CADnB,eAAgB,CAKhB,eAAgB,CANhB,iBAOJ,CAEA,0BAEI,kBAAmB,CAEnB,+CAAgD,CAHhD,YAAa,CAEb,YAEJ,CAEA,kBACI,MAAO,CACP,iBACJ,CAEA,cAEI,kBAAmB,CACnB,kCAAmC,CACnC,iBAAkB,CAHlB,YAAa,CAKb,WAAY,CADZ,SAEJ,CAEA,mBAII,sBAAuB,CAFvB,WAAY,CAIZ,4BAA6B,CAL7B,MAAO,CAIP,cAAe,CAFf,YAIJ,CAEA,yBAEI,eAAgB,CADhB,YAEJ,CAEA,oBAQI,kBACJ,CAEA,uCALI,kBAAmB,CAJnB,sBAAuB,CADvB,WAAY,CAEZ,4BAA6B,CAC7B,cAAe,CACf,YAeJ,CATA,mBAQI,iBAAkB,CADlB,WAEJ,CAEA,yBACI,kCACJ,CAEA,qBAGI,6BAA8B,CAF9B,gBAAiB,CACjB,eAEJ,CAEA,oBAEI,6BAA8B,CAD9B,+CAEJ,CAEA,+BACI,kBACJ,CAEA,yBAKI,6BAA8B,CAD9B,aAAc,CAHd,aAAc,CACd,YAAa,CACb,oBAGJ,CAEA,+BACI,kCACJ,CAEA,0BAII,4BAA6B,CAF7B,cAAe,CACf,eAAgB,CAFhB,cAIJ,CAEA,4BAGI,2BAA4B,CAD5B,cAAe,CAEf,eAAgB,CAHhB,QAIJ,CAEA,wBAKI,6BAA8B,CAF9B,2BAA4B,CAC5B,iBAAkB,CAHlB,iBAAkB,CAClB,iBAIJ,CAEA,kBACI,oCAAqC,CAGrC,iBAAkB,CAFlB,UAAY,CACZ,eAEJ,CAEA,0CACI,+BACJ,CAGA,qCACI,6BACJ,CAGA,oDACI,2BACJ,CAEA,oCAEI,6BAA8B,CAD9B,sCAEJ,CAEA,wBACI,6BACJ,CAMA,wFAEI,UACJ,CAEA,mCACI,6BACJ,CAEA,+BACI,6BACJ,CAEA,8BAEI,eAAgB,CADhB,sCAEJ,CAEA,mCACI,eACJ,CAEA,yCACI,+BACJ,CAMA,0EACI,UACJ,CAEA,kCACI,4BACJ,CCxNA,YACI,YAAa,CAEb,aAAc,CADd,6BAEJ,CAEA,cAII,6CAA8C,CAH9C,YAAa,CAEb,kBAAmB,CADnB,mBAGJ,CAEA,YAEI,gBAAiB,CACjB,sBAAwB,CAFxB,eAGJ,CAEA,4FACI,oDACJ,CAEA,aAEI,iCAAkC,CADlC,gBAEJ,CAEA,oBAGI,oBAAqB,CADrB,QAAS,CADT,SAGJ,CAEA,qBAEI,gDAAkD,CAClD,gBAAiB,CACjB,eAAgB,CAEhB,gBAAiB,CADjB,eAAgB,CAJhB,eAAgB,CAMhB,qBACJ,CAEA,8FACI,oDACJ,CAEA,oBACI,YAAa,CACb,aACJ,CAEA,wBAEI,WAAY,CACZ,qBAAsB,CAFtB,UAGJ,CAEA,YAII,0BAA2B,CAH3B,oBAAqB,CAErB,eAAgB,CAEhB,sBAAwB,CAHxB,eAIJ,CAEA,yBACI,YACI,yBACJ,CACJ,CCvEA,MAEI,kBAAmB,CADnB,eAEJ,CAEA,WAEI,gBAAiB,CACjB,gBAAiB,CAFjB,eAGJ,CAEA,kBAII,iCAAkC,CADlC,eAAgB,CADhB,eAAgB,CADhB,eAIJ,CAEA,YAII,iBAAkB,CAFlB,WAAY,CACZ,kBAAmB,CAFnB,UAIJ,CCvBA,uBACI,SACJ,CAEA,YAEI,gBAAiB,CACjB,eAAgB,CAEhB,sBAAwB,CADxB,eAAgB,CAHhB,eAAgB,CAKhB,qBACJ,CAEA,2CACI,UACJ,CAEA,iBACI,aACJ,CAEA,WAMI,iCAAkC,CAFlC,gBAAiB,CAGjB,sBAAwB,CAFxB,aAAc,CAFd,eAKJ,CAEA,wBARI,kBAAmB,CADnB,YAeJ,CANA,aAKI,mBAAoB,CAJpB,iBAAkB,CAClB,UAIJ,CAEA,wBAOI,iBAAkB,CALlB,aAAc,CAEd,WAAY,CACZ,aAAc,CACd,eAAgB,CALhB,iBAAkB,CAElB,UAKJ,CAEA,oCACI,UACJ,CAEA,qCACI,SACJ,CAEA,qCACI,SACJ,CAEA,qCACI,SACJ,CAEA,qCACI,SACJ,CAEA,mBAEI,mCAAoC,CACpC,iBAAkB,CAFlB,WAAY,CAGZ,mBAAiB,CAAjB,gBACJ,CAEA,kBAEI,eAAgB,CADhB,iBAEJ,CAEA,qDACI,YACJ,CAEA,kBACI,YACJ,CAEA,oBAEI,WAAY,CADZ,aAEJ,CAEA,cAII,iCAAkC,CAFlC,gBAAiB,CACjB,eAAgB,CAFhB,cAAe,CAIf,qBACJ,CAEA,WACI,YAAa,CACb,cAAe,CACf,eACJ,CAEA,UAQI,mCAAoC,CACpC,sCAAuC,CACvC,kBAAmB,CATnB,oBAAqB,CAGrB,gBAAiB,CACjB,eAAgB,CAEhB,qBAAuB,CADvB,aAAc,CAHd,kBAAmB,CADnB,gBASJ,CCvHA,iBAEI,YAAa,CACb,mBAAoB,CAFpB,iBAGJ,CAEA,uBACI,0CACJ,CAEA,0DACI,0BACJ,CAEA,uBACI,aAAc,CAGd,eAAgB,CADhB,iBAAkB,CADlB,WAGJ,CAEA,wCACI,aACJ,CAEA,yBACI,iBACI,aACJ,CAEA,uBAEI,kBAAmB,CADnB,UAEJ,CAEA,yBACI,aACJ,CACJ,CCtCA,YAEI,kBAAmB,CADnB,YAAa,CAGb,kBAAmB,CADnB,eAEJ,CAEA,2BAEI,sBAAuB,CAEvB,gBAAiB,CAHjB,QAAS,CAIT,eAAgB,CAFhB,eAGJ,CAEA,mCAEI,MAAO,CACP,eACJ,CAEA,kBACI,gBACJ,CAEA,sCACI,wBACJ,CAEA,aAMI,sCAAuC,CACvC,kBAAmB,CALnB,gBAAiB,CACjB,eAAgB,CAChB,aAAc,CAHd,YAOJ,CAEA,+BALI,iCAaJ,CARA,kBACI,aAAc,CAEd,gBAAiB,CACjB,eAAgB,CAFhB,iBAAkB,CAIlB,wBAAyB,CACzB,qBACJ,CC/CA,kBACI,kBACJ,CAMA,6DACI,kBACJ,CAEA,8BACI,gBAAiB,CACjB,eACJ,CAEA,4BACI,QACJ,CAEA,iBACI,kBACJ,CAEA,gGACI,oDACJ,CAEA,yBACI,kBAEI,kBAAmB,CADnB,YAEJ,CAEA,+BACI,kBACJ,CAEA,kEAEI,SACJ,CAEA,iBACI,kBACJ,CACJ,CC/CA,kBACI,+BACJ,CCFA,eACI,eACJ,CAEA,eAGI,iCAAkC,CADlC,cAAe,CAGf,kBAAmB,CAJnB,eAAgB,CAGhB,wBAEJ,CCVA,mBACI,eACJ,CCFA,cAGI,WAAY,CACZ,mBAAiB,CAAjB,gBAAiB,CAHjB,iBAAkB,CAClB,UAGJ,CAEA,aAII,QAAS,CACT,MAAO,CAEP,YAAa,CANb,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAIN,UAEJ,CAEA,eAGI,wCAAyC,CAFzC,iBAAkB,CAClB,UAEJ,CAEA,2BACI,QACJ,CAEA,0BACI,kBACJ,CAEA,yBACI,oBACJ,CAEA,sBACI,mBACJ,CAEA,WACI,iBACJ,CAEA,kBAQI,mCAAoC,CAJpC,QAAS,CAGT,UAAW,CAFX,MAAO,CAJP,iBAAkB,CAElB,OAAQ,CADR,KAAM,CAON,2CAA4C,CAH5C,UAIJ,CAEA,2BACI,0CACJ,CAEA,6CACI,UACJ,CAEA,yBACI,WACI,sBACJ,CACJ,CAEA,+CACI,WACI,sBACJ,CACJ,CAEA,+CACI,WACI,sBACJ,CACJ,CAEA,gDACI,WACI,sBACJ,CACJ,CAEA,0BACI,WACI,sBACJ,CACJ,CC3FA,KAGI,YAAa,CAFb,iBAAkB,CAGlB,UAAW,CAFX,SAAU,CAGV,uCACJ,CAEA,gBACI,iBAAkB,CAClB,kBACJ,CAEA,sBAGI,UAAW,CAGX,WAAY,CALZ,aAAc,CACd,QAAS,CAET,aAAc,CACd,iBAEJ,CAEA,sBAEI,eAEJ,CAEA,qCALI,iBAAkB,CAElB,uBAWJ,CARA,eAKI,0BAA2B,CAH3B,UAAW,CACX,cAAe,CAGf,uCAAwC,CACxC,0BACJ,CAEA,mBACI,aAAc,CACd,2BAA4B,CAC5B,UACJ,CAEA,+CAEI,YACJ,CAEA,4CAGI,cAAe,CACf,wBAAiB,CAAjB,qBAAiB,CAAjB,gBACJ,CAEA,8BAKI,kBAAmB,CAQnB,mCAAoC,CACpC,sCAAuC,CACvC,iBAAkB,CAJlB,4BAA6B,CAP7B,YAAa,CAMb,cAAe,CAFf,WAAY,CAFZ,sBAAuB,CAUvB,YAAa,CAPb,SAAU,CAPV,iBAAkB,CAUlB,iBAAkB,CATlB,SAAU,CAcV,0CAA4C,CAV5C,UAWJ,CAEA,gDAEI,iCAAkC,CAClC,cACJ,CAEA,eACI,UACJ,CAEA,eACI,OACJ,CAEA,eAGI,eACJ,CAEA,6BALI,YAAa,CACb,sBAaJ,CATA,cAEI,kBAAmB,CAKnB,QAAS,CAFT,WAAY,CAGZ,YAAa,CAFb,SAAU,CAFV,UAKJ,CAEA,mBAGI,sCAAuC,CACvC,iBAAkB,CAFlB,UAAW,CADX,SAIJ,CAEA,0BACI,mCACJ,CAEA,gBACI,aACJ,CAEA,iBACI,aAAc,CACd,SACJ,CAEA,gBACI,SACJ,CAEA,2BACI,iBACJ,CAEA,wBACI,wBAAiB,CAAjB,qBAAiB,CAAjB,gBACJ,CAEA,cACI,WACJ,CAEA,YACI,aACJ,CChJA,cAWA,2BAA4B,CAV5B,wBAAmC,CAKnC,iDAAkD,CAGlD,2BAA4B,CAD5B,2BAA4B,CAD5B,yBAA0B,CAG1B,wBAEA,CAEA,mBACA,sBAEA,CAMA,6GACA,wBACA,CAGA,gCACI,kBACJ,CAEA,iCACC,wBACD,CAEA,gCACI,kBAAmB,CACnB,wBACJ,CAEA,iCACI,kBAAmB,CACnB,wBACJ,CAEA,kCACI,kBAAmB,CACnB,wBACJ,CAEA,+BACI,kBAAmB,CACnB,wBACJ,CAEA,gCACI,kBAAmB,CACnB,wBACJ,CAEA,kCACI,kBAAmB,CACnB,wBACJ,CAEA,+BACI,wBAAyB,CACzB,UACJ,CAEA,8CACE,YACF,CAKA,6BACI,gCACJ,CACA,0BACC,6BACG,qBACJ,CACA,CAIA,EACC,uBACD,CAEA,eACE,aAAc,CACd,iBACF,CAEA,cACE,eACF,CAEA,gBACI,4BACJ,CAKA,cAWE,yBAA0B,CAC1B,iCAAkC,CATlC,8BAAqC,CACrC,oCAA2C,CAC3C,4BAA8B,CAS9B,qEACwC,CATxC,UAAY,CAIZ,cAAe,CAOf,oBAAqB,CAVrB,cAAe,CACf,eAAgB,CAUhB,eAAgB,CAFhB,yBAA2B,CAd3B,iBAAkB,CADlB,iBAAkB,CAQlB,8BAAgC,CAIhC,uBAMF,CAEA,qBAQE,4EAKC,CAXD,UAAW,CAKX,WAAY,CAFZ,UAAW,CAJX,SAAU,CAEV,iBAAkB,CAClB,KAAM,CAUN,wBAA0B,CAR1B,UASF,CAEA,2BACE,SACF,CAEA,oBACE,8BAAqC,CACrC,+BAAsC,CAEtC,uEACwC,CAFxC,oCAGF,CAEA,2BACE,SACF,CAEA,qBAEE,qEACwC,CAFxC,uBAGF,CAGA,kBAIE,kBAAmB,CAHnB,YAAa,CACb,qBAAsB,CACtB,QAAS,CAET,eACF,CAEA,wBACE,8BAAqC,CACrC,oCACF,CAEA,8BACE,6BAAoC,CACpC,gCACF,CAOC,iBAGK,kBACF,CAEA,aACE,uBAAkC,CAClC,yBAAoC,CACpC,2BAAsC,CACtC,yBAAqC,CACrC,yBAAqC,CACrC,mBAA4B,CAC5B,sBAAgC,CAChC,4BAAqC,CACrC,8BAAwC,CACxC,2BAAuC,CACvC,6BAA0C,CAC1C,gCAA4C,CAC5C,6BAAyC,CACzC,yBAAoC,CACpC,4BAAsC,CACtC,2BAAqC,CACrC,oCAAuC,CACvC,sCAAyC,CACzC,wCAA2C,CAC3C,uCAA0C,CAC1C,uCAA0C,CAC1C,8BAAiC,CACjC,kCAAqC,CACrC,uCAA0C,CAC1C,0CAA6C,CAC7C,yCAA4C,CAC5C,4CAA+C,CAC/C,8CAAiD,CACjD,2CAA8C,CAC9C,sCAAyC,CACzC,wCAA2C,CAC3C,uCACF,CACA,YACE,uBAAmC,CACnC,yBAAqC,CACrC,2BAAuC,CACvC,sBAA+B,CAC/B,yBAAkC,CAClC,mBAA+B,CAC/B,mBAAkC,CAClC,4BAAwC,CACxC,8BAA0C,CAC1C,wBAAuC,CACvC,8BAA0C,CAC1C,gCAAyC,CACzC,6BAAsC,CACtC,yBAAqC,CACrC,4BAAwC,CACxC,2BAAqC,CACrC,qCAAwC,CACxC,uCAA0C,CAC1C,yCAA4C,CAC5C,iCAAoC,CACpC,oCAAuC,CACvC,iCAAoC,CACpC,oCAAuC,CACvC,0CAA6C,CAC7C,4CAA+C,CAC/C,yCAA4C,CAC5C,4CAA+C,CAC/C,2CAA8C,CAC9C,wCAA2C,CAC3C,uCAA0C,CAC1C,0CAA6C,CAC7C,uCACF", "file": "screen.css", "sourcesContent": [":root {\n    --color-primary-text: #333;\n    --color-secondary-text: #999;\n    --color-white: #fff;\n    --color-lighter-gray: #f6f6f6;\n    --color-light-gray: #e6e6e6;\n    --color-mid-gray: #ccc;\n    --color-dark-gray: #444;\n    --color-darker-gray: #15171a;\n    --color-black: #000;\n    --font-sans: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Arial, sans-serif; /* stylelint-disable-line value-keyword-case */\n    --font-serif: Georgia, serif; /* stylelint-disable-line value-keyword-case */\n    --font-mono: Menlo, Consolas, Monaco, Liberation Mono, Lucida Console, monospace; /* stylelint-disable-line value-keyword-case */\n    --head-nav-gap: 2.8rem;\n    --h1-size: 4.6rem;\n    --gap: 3.6rem;\n    --header-spacing: 80px;\n}\n\n@media (max-width: 767px) {\n    :root {\n        --h1-size: 3.2rem !important;\n        --gap: 2rem;\n        --header-spacing: 48px;\n    }\n}\n", "/* Box sizing rules */\n*,\n*::before,\n*::after {\n    box-sizing: border-box;\n}\n\n/* Remove default margin */\nbody,\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\np,\nfigure,\nblockquote,\ndl,\ndd {\n    margin: 0;\n}\n\n/* Remove list styles on ul, ol elements with a list role, which suggests default styling will be removed */\nul[role=\"list\"],\nol[role=\"list\"] {\n    list-style: none;\n}\n\n/* Set core root defaults */\nhtml:focus-within {\n    scroll-behavior: smooth;\n}\n\n/* Set core body defaults */\nbody {\n    min-height: 100vh;\n    line-height: 1.5;\n    text-rendering: optimizespeed;\n}\n\n/* A elements that don't have a class get default styles */\na:not([class]) {\n    text-decoration-skip-ink: auto;\n}\n\n/* Make images easier to work with */\nimg,\npicture {\n    display: block;\n    max-width: 100%;\n}\n\n/* Inherit fonts for inputs and buttons */\ninput,\nbutton,\ntextarea,\nselect {\n    font: inherit;\n}\n\n/* Remove all animations and transitions for people that prefer not to see them */\n@media (prefers-reduced-motion: reduce) {\n    html:focus-within {\n        scroll-behavior: auto;\n    }\n\n    *,\n    *::before,\n    *::after {\n        transition-duration: 0.01ms !important;\n        animation-duration: 0.01ms !important;\n        animation-iteration-count: 1 !important;\n        scroll-behavior: auto !important;\n    }\n}\n", "html {\n    font-size: 62.5%;\n}\n\nbody {\n    font-family: var(--gh-font-body, var(--font-sans));\n    font-size: 1.6rem;\n    line-height: 1.6;\n    color: var(--color-primary-text);\n    background-color: var(--color-white);\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n}\n\nimg {\n    height: auto;\n}\n\na {\n    color: var(--color-darker-gray);\n    text-decoration: none;\n}\n\na:hover {\n    opacity: 0.8;\n}\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6 {\n    font-family: var(--gh-font-heading, var(--font-sans));\n    line-height: 1.15;\n    color: var(--color-darker-gray);\n    letter-spacing: -0.02em;\n}\n\nh1 {\n    font-size: var(--h1-size);\n}\n\nh2 {\n    font-size: 2.8rem;\n}\n\nh3 {\n    font-size: 2.4rem;\n}\n\nh4 {\n    font-size: 2.2rem;\n}\n\nh5 {\n    font-size: 2rem;\n}\n\nh6 {\n    font-size: 1.8rem;\n}\n\nhr {\n    width: 100%;\n    height: 1px;\n    background-color: var(--color-light-gray);\n    border: 0;\n}\n\nblockquote:not([class]) {\n    padding-left: 2rem;\n    border-left: 4px solid var(--ghost-accent-color);\n}\n\nfigcaption {\n    margin-top: 1.6rem;\n    font-size: 1.4rem;\n    line-height: 1.4;\n    color: var(--color-secondary-text);\n    text-align: center;\n}\n\n.kg-width-full figcaption {\n    padding: 0 1.6rem;\n}\n\n.gh-content figcaption a {\n    color: var(--color-darker-gray);\n    text-decoration: none;\n}\n\npre {\n    padding: 1.6rem 2.4rem;\n    overflow-x: scroll;\n    hyphens: none;\n    line-height: 1.5;\n    white-space: pre;\n    background-color: var(--color-lighter-gray);\n    -webkit-overflow-scrolling: touch;\n}\n\ncode {\n    font-family: var(--font-mono);\n    font-size: 15px;\n}\n\n:not(pre) > code {\n    padding: 0.4rem;\n    color: var(--ghost-accent-color);\n    background-color: var(--color-lighter-gray);\n    border-radius: 3px;\n}\n\niframe {\n    display: block;\n    width: 100%;\n    overflow: hidden;\n    border: 0;\n}\n\n@media (max-width: 767px) {\n    h2 {\n        font-size: 2.4rem;\n    }\n\n    h3 {\n        font-size: 2.1rem;\n    }\n}\n", ".gh-site {\n    display: flex;\n    flex-direction: column;\n    min-height: 100vh;\n}\n\n.gh-main {\n    flex-grow: 1;\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n}\n\n.page-template .gh-main {\n    padding-block: 0;\n}\n\n.gh-outer {\n    padding-right: var(--gap);\n    padding-left: var(--gap);\n}\n\n.gh-inner {\n    max-width: var(--container-width, 1200px);\n    margin: 0 auto;\n}\n\n/* <PERSON><PERSON> creates a multi-column, centered grid which the post\nis laid out on top of. <PERSON><PERSON> just defines the grid, we don't\nuse it for applying any other styles. */\n\n.gh-canvas,\n.kg-width-full.kg-content-wide {\n    --main: min(var(--content-width, 720px), 100% - var(--gap) * 2);\n    --wide: minmax(0, calc((var(--container-width, 1200px) - var(--content-width, 720px)) / 2));\n    --full: minmax(var(--gap), 1fr);\n\n    display: grid;\n    grid-template-columns:\n        [full-start] var(--full)\n        [wide-start] var(--wide)\n        [main-start] var(--main) [main-end]\n        var(--wide) [wide-end]\n        var(--full) [full-end];\n}\n\n.gh-canvas > * {\n    grid-column: main;\n}\n\n.kg-width-wide,\n.kg-content-wide > div {\n    grid-column: wide;\n}\n\n.kg-width-full {\n    grid-column: full;\n}\n\n.kg-width-full img {\n    width: 100%;\n}\n\n@media (max-width: 767px) {\n    #gh-main {\n        padding-top: 4.8rem;\n        padding-bottom: 4.8rem;\n    }\n}\n", ".gh-head {\n    height: 100px;\n    background-color: var(--color-white);\n}\n\n.gh-head-inner {\n    display: grid;\n    grid-template-columns: 1fr auto auto;\n    grid-auto-flow: row dense;\n    column-gap: var(--head-nav-gap);\n    align-items: center;\n    height: 100%;\n}\n\n.gh-head-brand {\n    line-height: 1;\n}\n\n.gh-head-brand-wrapper {\n    display: flex;\n    align-items: center;\n}\n\n.gh-head-logo {\n    position: relative;\n    font-family: var(--gh-font-heading, var(--font-sans));\n    font-size: 2.4rem;\n    font-weight: 700;\n    letter-spacing: -0.02em;\n}\n\n.gh-head-logo img {\n    max-height: 40px;\n}\n\n.gh-head-logo img:nth-child(2) {\n    position: absolute;\n    top: 0;\n    left: 0;\n    opacity: 0;\n}\n\n.gh-head-menu {\n    display: flex;\n    gap: var(--head-nav-gap);\n    align-items: center;\n}\n\n.gh-head .nav {\n    display: inline-flex;\n    gap: var(--head-nav-gap);\n    align-items: center;\n    padding: 0;\n    margin: 0;\n    white-space: nowrap;\n    list-style: none;\n}\n\n.gh-head .nav-more-toggle {\n    position: relative;\n    margin: 0 -6px;\n    font-size: inherit;\n    text-transform: inherit;\n}\n\n.gh-head .nav-more-toggle svg {\n    width: 24px;\n    height: 24px;\n}\n\n.gh-head-actions {\n    display: flex;\n    gap: var(--head-nav-gap);\n    align-items: center;\n    justify-content: flex-end;\n}\n\n.gh-head-members {\n    display: flex;\n    gap: 20px;\n    align-items: center;\n    white-space: nowrap;\n}\n\n.gh-head-btn.gh-btn {\n    font-size: inherit;\n    font-weight: 600;\n}\n\n.gh-head-btn:not(.gh-btn) {\n    display: inline-flex;\n    align-items: center;\n    color: var(--ghost-accent-color);\n}\n\n.gh-head-btn svg {\n    width: 1.3em;\n    height: 1.3em;\n}\n\n.gh-search {\n    margin-right: -6px;\n    margin-left: -6px;\n}\n\n.gh-search:hover {\n    opacity: 0.9;\n}\n\n.gh-head-brand .gh-search {\n    margin-right: 8px;\n}\n\n@media (max-width: 767px) {\n    .gh-head-members {\n        flex-direction: column-reverse;\n        gap: 16px;\n        width: 100%;\n    }\n\n    .gh-head-actions .gh-search {\n        display: none;\n    }\n}\n\n@media (min-width: 768px) {\n    .gh-head-brand .gh-search {\n        display: none;\n    }\n\n    body:not(.is-dropdown-loaded) .gh-head-menu .nav > li {\n        opacity: 0;\n    }\n}\n\n/* Header variants\n/* ---------------------------------------------------------- */\n\n/*\n======================================================================\nLOGO   Home About Collection Author Portal             Login Subscribe\n======================================================================\n*/\n\n.is-head-left-logo .gh-head-inner {\n    grid-template-columns: auto 1fr auto;\n}\n\n@media (min-width: 992px) {\n    .is-head-left-logo .gh-head-menu {\n        margin-right: 64px;\n        margin-left: 16px;\n    }\n}\n\n/*\n======================================================================\nHome About Collection            LOGO                  Login Subscribe\n======================================================================\n*/\n\n.is-head-middle-logo .gh-head-inner {\n    grid-template-columns: 1fr auto 1fr;\n}\n\n.is-head-middle-logo .gh-head-brand {\n    grid-column-start: 2;\n}\n\n.is-head-middle-logo .gh-head-actions {\n    gap: 28px;\n}\n\n@media (min-width: 992px) {\n    .is-head-middle-logo .gh-head-menu {\n        margin-right: 64px;\n    }\n}\n\n/*\n======================================================================\nSearch                         LOGO                    Login Subscribe\n                 Home About Collection Author Portal\n======================================================================\n*/\n\n.is-head-stacked .gh-head {\n    position: relative;\n    height: auto;\n}\n\n.is-head-stacked .gh-head-inner {\n    grid-template-columns: 1fr auto 1fr;\n}\n\n.is-head-stacked .gh-head-brand {\n    display: flex;\n    grid-row-start: 1;\n    grid-column-start: 2;\n    min-height: 80px;\n}\n\n@media (max-width: 767px) {\n    .is-head-stacked .gh-head-brand {\n        min-height: unset;\n    }\n}\n\n@media (min-width: 992px) {\n    .is-head-stacked .gh-head-inner {\n        padding: 0;\n    }\n\n    .is-head-stacked .gh-head-brand {\n        display: flex;\n        align-items: center;\n        height: 80px;\n    }\n\n    .is-head-stacked .gh-head-menu {\n        grid-row-start: 2;\n        grid-column: 1 / 4;\n        justify-content: center;\n        height: 56px;\n        margin: 0 48px;\n    }\n\n    .is-head-stacked .gh-head-menu::before,\n    .is-head-stacked .gh-head-menu::after {\n        position: absolute;\n        top: 80px;\n        left: 0;\n        width: 100%;\n        height: 1px;\n        content: \"\";\n        background-color: var(--color-light-gray);\n    }\n\n    .is-head-stacked .gh-head-menu::after {\n        top: 136px;\n    }\n\n    .is-head-stacked .gh-head-actions {\n        grid-row-start: 1;\n        grid-column: 1 / 4;\n        justify-content: space-between;\n    }\n}\n\n/* Transparent header\n/* ---------------------------------------------------------- */\n\n.is-head-transparent .gh-head {\n    position: absolute;\n    right: 0;\n    left: 0;\n    z-index: 90;\n    background-color: transparent;\n}\n\n.is-head-transparent .gh-head-logo,\n.is-head-transparent .gh-head-logo a,\n.is-head-transparent .gh-head .nav > li a,\n.is-head-transparent .gh-head-description,\n.is-head-transparent .gh-search,\n.is-head-transparent .gh-social,\n.is-head-transparent .nav-more-toggle,\n.is-head-transparent .gh-head-link {\n    color: var(--color-white);\n}\n\n.is-head-transparent .gh-burger::before,\n.is-head-transparent .gh-burger::after {\n    background-color: var(--color-white);\n}\n\n.is-head-transparent .gh-head-btn {\n    color: #15171a;\n    background-color: #fff;\n}\n\n.is-head-transparent .gh-head-menu::before,\n.is-head-transparent .gh-head-menu::after {\n    background-color: rgba(255, 255, 255, 0.2);\n}\n\n.is-head-transparent #announcement-bar-root {\n    position: absolute;\n    right: 0;\n    left: 0;\n}\n\n.is-head-transparent #announcement-bar-root:not(:empty) + :is(.site, .gh-site) .gh-head {\n    margin-top: 48px;\n}\n\n/* Dark header\n/* ---------------------------------------------------------- */\n\n.is-head-dark:not(.is-head-transparent) .gh-head {\n    background-color: var(--color-darker-gray);\n}\n\n.is-head-dark:not(.is-head-transparent) .gh-head-logo,\n.is-head-dark:not(.is-head-transparent) .gh-head-logo a,\n.is-head-dark:not(.is-head-transparent) .gh-head .nav a,\n.is-head-dark:not(.is-head-transparent) .gh-head-description,\n.is-head-dark:not(.is-head-transparent) .gh-search,\n.is-head-dark:not(.is-head-transparent) .gh-social,\n.is-head-dark:not(.is-head-transparent) .nav-more-toggle,\n.is-head-dark:not(.is-head-transparent) .gh-head-link {\n    color: var(--color-white);\n}\n\n.is-head-dark:not(.is-head-transparent) .gh-burger::before,\n.is-head-dark:not(.is-head-transparent) .gh-burger::after {\n    background-color: var(--color-white);\n}\n\n.is-head-dark:not(.is-head-transparent) .gh-head-btn {\n    color: #15171a;\n    background-color: #fff;\n}\n\n.is-head-dark:not(.is-head-transparent) .gh-head-menu::before,\n.is-head-dark:not(.is-head-transparent) .gh-head-menu::after {\n    background-color: rgba(255, 255, 255, 0.2);\n}\n\n/* Brand header\n/* ---------------------------------------------------------- */\n\n.is-head-brand:not(.is-head-transparent) .gh-head {\n    background-color: var(--ghost-accent-color);\n}\n\n.is-head-brand:not(.is-head-transparent) .gh-head-logo,\n.is-head-brand:not(.is-head-transparent) .gh-head-logo a,\n.is-head-brand:not(.is-head-transparent) .gh-head .nav a,\n.is-head-brand:not(.is-head-transparent) .gh-head-description,\n.is-head-brand:not(.is-head-transparent) .gh-search,\n.is-head-brand:not(.is-head-transparent) .gh-social,\n.is-head-brand:not(.is-head-transparent) .nav-more-toggle,\n.is-head-brand:not(.is-head-transparent) .gh-head-link {\n    color: var(--color-white);\n}\n\n.is-head-brand:not(.is-head-transparent) .gh-burger::before,\n.is-head-brand:not(.is-head-transparent) .gh-burger::after {\n    background-color: var(--color-white);\n}\n\n.is-head-brand:not(.is-head-transparent) .gh-head-btn {\n    color: #15171a;\n    background-color: #fff;\n}\n\n.is-head-brand:not(.is-head-transparent) .gh-head-menu::before,\n.is-head-brand:not(.is-head-transparent) .gh-head-menu::after {\n    background-color: rgba(255, 255, 255, 0.3);\n}\n\n/* Dropdown menu\n/* ---------------------------------------------------------- */\n\n.gh-dropdown {\n    position: absolute;\n    top: 100%;\n    right: -16px;\n    z-index: 90;\n    width: 200px;\n    padding: 12px 0;\n    margin-top: 24px;\n    text-align: left;\n    visibility: hidden;\n    background-color: #fff;\n    border-radius: 5px;\n    box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.04), 0 7px 20px -5px rgba(0, 0, 0, 0.15);\n    opacity: 0;\n    transition: opacity 0.3s, transform 0.2s;\n    transform: translate3d(0, 6px, 0);\n}\n\n.is-head-middle-logo .gh-dropdown {\n    right: auto;\n    left: -24px;\n}\n\n.is-dropdown-mega .gh-dropdown {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    grid-auto-flow: column;\n    column-gap: 40px;\n    width: auto;\n    padding: 20px 32px;\n}\n\n.is-dropdown-open .gh-dropdown {\n    visibility: visible;\n    opacity: 1;\n    transform: translateY(0);\n}\n\n.gh-dropdown li a {\n    display: block;\n    padding: 6px 20px;\n    color: #15171a !important;\n}\n\n.is-dropdown-mega .gh-dropdown li a {\n    padding: 8px 0;\n}\n\n/* Mobile menu\n/* ---------------------------------------------------------- */\n\n.gh-burger {\n    position: relative;\n    display: none;\n    width: 30px;\n    height: 30px;\n    padding: 0;\n    margin-right: -3px;\n    appearance: none;\n    cursor: pointer;\n    background-color: transparent;\n    border: 0;\n}\n\n.gh-burger::before,\n.gh-burger::after {\n    position: absolute;\n    left: 3px;\n    width: 24px;\n    height: 1px;\n    content: \"\";\n    background-color: var(--color-darker-gray);\n    transition: all 0.2s cubic-bezier(0.04, 0.04, 0.12, 0.96) 0.1008s;\n}\n\n.gh-burger::before {\n    top: 11px;\n}\n\n.gh-burger::after {\n    bottom: 11px;\n}\n\n.is-head-open .gh-burger::before {\n    top: 15px;\n    transform: rotate(45deg);\n}\n\n.is-head-open .gh-burger::after {\n    bottom: 14px;\n    transform: rotate(-45deg);\n}\n\n@media (max-width: 767px) {\n    #gh-head {\n        height: 64px;\n    }\n\n    #gh-head .gh-head-inner {\n        grid-template-rows: auto 1fr auto;\n        grid-template-columns: 1fr;\n        gap: 48px;\n    }\n\n    #gh-head .gh-head-brand {\n        display: grid;\n        grid-template-columns: 1fr auto auto;\n        grid-column-start: 1;\n        align-items: center;\n        height: 64px;\n    }\n\n    #gh-head .gh-head-logo {\n        font-size: 2.2rem;\n    }\n\n    .gh-burger {\n        display: block;\n    }\n\n    #gh-head .gh-head-menu,\n    #gh-head .gh-head-actions {\n        position: fixed;\n        justify-content: center;\n        visibility: hidden;\n        opacity: 0;\n    }\n\n    #gh-head .gh-head-menu {\n        transition: none;\n        transform: translateY(0);\n    }\n\n    #gh-head .nav {\n        gap: 20px;\n        align-items: center;\n        line-height: 1.4;\n    }\n\n    #gh-head .nav a {\n        font-size: 2.6rem;\n        font-weight: 600;\n        text-transform: none;\n    }\n\n    #gh-head .nav li {\n        opacity: 0;\n        transform: translateY(-4px);\n    }\n\n    #gh-head .gh-head-actions {\n        text-align: center;\n    }\n\n    #gh-head :is(.gh-head-btn, .gh-head-link) {\n        opacity: 0;\n        transform: translateY(8px);\n    }\n\n    #gh-head .gh-head-btn {\n        width: 100%;\n        font-size: 1.8rem;\n        text-transform: none;\n    }\n\n    #gh-head .gh-head-btn:not(.gh-btn) {\n        font-size: 2rem;\n    }\n\n    #gh-main {\n        transition: opacity 0.4s;\n    }\n\n    .is-head-open #gh-head {\n        position: fixed;\n        inset: 0;\n        z-index: 3999999;\n        height: 100%;\n        overflow-y: scroll;\n        -webkit-overflow-scrolling: touch;\n    }\n\n    .is-head-open:not(.is-head-brand):not(.is-head-dark):not(.is-head-transparent) #gh-head {\n        background-color: var(--color-white);\n    }\n\n    .is-head-open.is-head-transparent #gh-head,\n    .is-head-open:is(.is-head-transparent, .is-head-brand) #gh-head .gh-head-actions {\n        background-color: var(--ghost-accent-color);\n    }\n\n    .is-head-open.is-head-dark #gh-head,\n    .is-head-open.is-head-dark #gh-head .gh-head-actions {\n        background-color: var(--color-darker-gray);\n    }\n\n    .is-head-open #gh-head .gh-head-menu,\n    .is-head-open #gh-head .gh-head-actions {\n        position: static;\n        visibility: visible;\n        opacity: 1;\n    }\n\n    .is-head-open #gh-head .nav {\n        display: flex;\n        flex-direction: column;\n    }\n\n    .is-head-open #gh-head .nav li {\n        opacity: 1;\n        transition: transform 0.2s, opacity 0.2s;\n        transform: translateY(0);\n    }\n\n    .is-head-open #gh-head .gh-head-actions {\n        position: sticky;\n        right: 0;\n        bottom: 0;\n        left: 0;\n        display: inline-flex;\n        flex-direction: column;\n        gap: 12px;\n        align-items: center;\n        padding: var(--gap) 0 calc(var(--gap) + 8px);\n        background-color: var(--color-white);\n    }\n\n    .is-head-open #gh-head :is(.gh-head-btn, .gh-head-link) {\n        opacity: 1;\n        transition: transform 0.4s, opacity 0.4s;\n        transition-delay: 0.2s;\n        transform: translateY(0);\n    }\n\n    .is-head-open #gh-head .gh-head-link {\n        transition-delay: 0.4s;\n    }\n\n    .is-head-open #gh-main {\n        opacity: 0;\n    }\n}\n", ".gh-cover {\n    position: relative;\n    min-height: var(--cover-height, 50vh);\n    padding-top: 6.4rem;\n    padding-bottom: 6.4rem;\n}\n\n.gh-cover-image {\n    position: absolute;\n    inset: 0;\n    z-index: -1;\n    object-fit: cover;\n    width: 100%;\n    height: 100%;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-cover-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-cover-description {\n    font-family: var(--gh-font-body, var(--font-serif));\n}\n", ".has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-card-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n    letter-spacing: -0.01em;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-card-excerpt {\n    font-family: var(--gh-font-body, var(--font-serif));\n}\n", ".gh-card {\n    word-break: break-word;\n}\n", ".page-template .gh-article-header {\n    margin-top: var(--header-spacing, 80px);\n}\n\n.gh-article-tag {\n    color: var(--ghost-accent-color);\n}\n\n.gh-article-title {\n    word-break: break-word;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-article-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n    letter-spacing: -0.01em;\n}\n\n.gh-article-excerpt {\n    margin-top: 2rem;\n    font-size: 2.1rem;\n    line-height: 1.5;\n    color: var(--color-secondary-text);\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-article-excerpt {\n    font-family: var(--gh-font-body, var(--font-serif));\n}\n\n.gh-article-image {\n    grid-column: wide-start/wide-end;\n    margin-top: 4rem;\n}\n\n.gh-article-image img {\n    width: 100%;\n}\n\n@media (max-width: 767px) {\n    .gh-article-excerpt {\n        font-size: 1.9rem;\n    }\n\n    .gh-article-image {\n        margin-top: 2.4rem;\n    }\n}\n", "/* Content refers to styling all page and post content that is\ncreated within the Ghost editor. The main content handles\nheadings, text, images and lists. We deal with cards lower down. */\n\n.gh-content {\n    margin-top: 4rem;\n    font-size: var(--content-font-size, 1.8rem);\n    letter-spacing: var(--content-letter-spacing, 0);\n    word-break: break-word;\n}\n\n/* Default vertical spacing */\n.gh-content > * + * {\n    margin-top: calc(1.6em * var(--content-spacing-multiplier, 1));\n    margin-bottom: 0;\n}\n\n/* [id] represents all headings h1-h6, reset all margins */\n.gh-content > [id] {\n    margin: 0;\n}\n\n.gh-content > .kg-card + [id] {\n    margin-top: calc(2em * var(--content-spacing-multiplier, 1)) !important;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .gh-content > [id] {\n    font-family: var(--gh-font-heading, var(--font-serif));\n    letter-spacing: -0.01em;\n}\n\n/* Add back a top margin to all headings, unless a heading\nis the very first element in the post content */\n.gh-content > [id]:not(:first-child) {\n    margin-top: calc(1.6em * var(--content-spacing-multiplier, 1));\n}\n\n/* Add a small margin between a heading and anything after it */\n.gh-content > [id] + * {\n    margin-top: calc(0.8em * var(--content-spacing-multiplier, 1));\n}\n\n/* A larger margin before/after HRs and blockquotes */\n.gh-content > hr,\n.gh-content > blockquote {\n    position: relative;\n    margin-top: calc(2.4em * var(--content-spacing-multiplier, 1));\n}\n\n.gh-content > hr + *,\n.gh-content > blockquote + * {\n    margin-top: calc(2.4em * var(--content-spacing-multiplier, 1));\n}\n\n/* Now the content typography styles */\n.gh-content h2 {\n    font-size: 1.6em;\n}\n\n.gh-content h3 {\n    font-size: 1.4em;\n}\n\n.gh-content a {\n    color: var(--ghost-accent-color);\n    text-decoration: underline;\n    word-break: break-word;\n}\n\n.gh-content .kg-callout-card .kg-callout-text,\n.gh-content .kg-toggle-card .kg-toggle-content > ol,\n.gh-content .kg-toggle-card .kg-toggle-content > ul,\n.gh-content .kg-toggle-card .kg-toggle-content > p {\n    font-size: 0.95em;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > blockquote,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > ol,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > ul,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > dl,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content > p,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-callout-text,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-content > ol,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-content > ul,\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .gh-content .kg-toggle-content > p {\n    font-family: var(--gh-font-body, var(--font-serif));\n}\n\n.gh-content ul,\n.gh-content ol,\n.gh-content dl {\n    padding-left: 2.8rem;\n}\n\n.gh-content :is(li + li, li :is(ul, ol)) {\n    margin-top: 0.8rem;\n}\n\n.gh-content ol ol li {\n    list-style-type: lower-alpha;\n}\n\n.gh-content ol ol ol li {\n    list-style-type: lower-roman;\n}\n\n.gh-content table:not(.gist table) {\n    display: inline-block;\n    width: auto;\n    max-width: 100%;\n    overflow-x: auto;\n    font-family: var(--font-sans);\n    font-size: 1.6rem;\n    white-space: nowrap;\n    vertical-align: top;\n    border-spacing: 0;\n    border-collapse: collapse;\n    -webkit-overflow-scrolling: touch;\n    background: radial-gradient(ellipse at left, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 75%) 0 center, radial-gradient(ellipse at right, rgba(0, 0, 0, 0.2) 0%, rgba(0, 0, 0, 0) 75%) 100% center;\n    background-repeat: no-repeat;\n    background-attachment: scroll, scroll;\n    background-size: 10px 100%, 10px 100%;\n}\n\n.gh-content table:not(.gist table) td:first-child {\n    background-image: linear-gradient(to right, rgba(255,255,255, 1) 50%, rgba(255,255,255, 0) 100%);\n    background-repeat: no-repeat;\n    background-size: 20px 100%;\n}\n\n.gh-content table:not(.gist table) td:last-child {\n    background-image: linear-gradient(to left, rgba(255,255,255, 1) 50%, rgba(255,255,255, 0) 100%);\n    background-repeat: no-repeat;\n    background-position: 100% 0;\n    background-size: 20px 100%;\n}\n\n.gh-content table:not(.gist table) th {\n    font-size: 1.2rem;\n    font-weight: 700;\n    color: var(--color-darkgrey);\n    text-align: left;\n    text-transform: uppercase;\n    letter-spacing: 0.2px;\n    background-color: var(--color-white);\n}\n\n.gh-content table:not(.gist table) th,\n.gh-content table:not(.gist table) td {\n    padding: 6px 12px;\n    border: 1px solid var(--color-light-gray);\n}\n\n/* Page without header */\n.page-template .gh-content:only-child {\n    margin-top: 0 !important;\n}\n\n.page-template .gh-content:only-child > *:first-child:not(.kg-width-full) {\n    margin-top: var(--header-spacing, 80px);\n}\n\n.page-template .gh-content > *:last-child:not(.kg-width-full) {\n    margin-bottom: var(--footer-spacing, 0);\n}\n\n@media (max-width: 767px) {\n    .gh-content {\n        margin-top: 3.2rem;\n        font-size: 1.7rem;\n    }\n}\n", ".gh-comments {\n    margin-top: 64px;\n    margin-bottom: -24px;\n}\n\n.gh-comments-header {\n    display: flex;\n    align-items: center;\n    justify-content: space-between;\n    margin-bottom: 40px;\n}\n\n.gh-comments .gh-comments-title {\n    margin-bottom: 28px;\n}\n\n.gh-comments-header .gh-comments-title {\n    margin-bottom: 0;\n}\n\n.gh-comments-count {\n    color: var(--color-secondary-text);\n}\n", ".gh-cta-gradient {\n    position: relative;\n    grid-column: full-start / full-end;\n    height: 160px;\n    margin-top: -16rem;\n    margin-bottom: 4rem;\n    content: \"\";\n    background: linear-gradient(180deg, rgba(255, 255, 255, 0), var(--color-white));\n}\n\n.gh-cta-gradient:first-child {\n    display: none;\n}\n\n.gh-cta {\n    display: flex;\n    flex-direction: column;\n    margin-bottom: 4rem;\n    text-align: center;\n}\n\n.gh-cta-title {\n    margin-bottom: 3.2rem;\n    font-size: 2.2rem;\n    letter-spacing: -0.02em;\n}\n\n.gh-cta-actions {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n}\n\n.gh-cta-link {\n    margin-top: 0.8rem;\n    font-size: 1.4rem;\n    color: var(--color-secondary-text);\n    text-decoration: none;\n    cursor: pointer;\n}\n\n.gh-cta-link:hover {\n    color: var(--color-darker-gray);\n}\n\n@media (max-width: 767px) {\n    .gh-cta-actions {\n        column-gap: 0.8rem;\n    }\n}\n", "/* Add extra margin before/after any cards,\nexcept for when immediately preceeded by a heading */\n\n.gh-content :not(.kg-card):not(table):not([id]) + :is(.kg-card, table) {\n    margin-top: calc(2em * var(--content-spacing-multiplier, 1));\n}\n\n.gh-content :is(.kg-card, table) + :not(.kg-card):not(table):not([id]) {\n    margin-top: calc(2em * var(--content-spacing-multiplier, 1));\n}\n\n/* Remove space between full-width cards */\n.gh-content > .kg-width-full + .kg-width-full:not(.kg-width-full.kg-card-hascaption + .kg-width-full) {\n    margin-top: 0;\n}\n\n/* Image\n/* ---------------------------------------------------------- */\n\n.kg-image {\n    margin-right: auto;\n    margin-left: auto;\n}\n\n/* Embed\n/* ---------------------------------------------------------- */\n\n.kg-embed-card {\n    display: flex;\n    flex-direction: column;\n    align-items: center;\n    width: 100%;\n}\n\n/* Gallery\n/* ---------------------------------------------------------- */\n\n.kg-image[width][height],\n.kg-gallery-image {\n    cursor: pointer;\n}\n\n.kg-image-card a:hover,\n.kg-gallery-image a:hover {\n    opacity: 1 !important;\n}\n\n/* Toggle\n/* ---------------------------------------------------------- */\n\n.kg-card.kg-toggle-card .kg-toggle-heading-text {\n    font-size: 2rem;\n    font-weight: 700;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-toggle-card .kg-toggle-heading-text {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n/* Callout\n/* ---------------------------------------------------------- */\n\n.kg-callout-card.kg-card {\n    border-radius: 0.25em;\n}\n\n.kg-callout-card-accent a {\n    text-decoration: underline;\n}\n\n/* Blockquote\n/* ---------------------------------------------------------- */\n\nblockquote.kg-blockquote-alt {\n    font-style: normal;\n    font-weight: 400;\n    color: var(--color-secondary-text);\n}\n\n/* Button\n/* ---------------------------------------------------------- */\n\n.kg-card.kg-button-card .kg-btn {\n    font-size: 1em;\n}\n\n/* Header\n/* ---------------------------------------------------------- */\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .kg-card.kg-header-card h2.kg-header-card-header {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .kg-header-card h3.kg-header-card-subheader {\n    font-family: var(--gh-font-body, var(--font-serif));\n}\n\n/* Bookmark\n/* ---------------------------------------------------------- */\n\n.kg-bookmark-card a.kg-bookmark-container,\n.kg-bookmark-card a.kg-bookmark-container:hover,\n.kg-file-card-container,\n.kg-file-card-container:hover,\n.kg-audio-card,\n.kg-product-card-container {\n    background: var(--background-color, #fff) !important;\n    color: var(--color-darker-gray, #222) !important;\n    opacity: 1;\n}\n\n.kg-bookmark-card .kg-bookmark-container {\n    border-radius: 0.25em !important;\n}\n\n.kg-bookmark-card .kg-bookmark-content {\n    padding: 1.15em;\n}\n\n.kg-bookmark-card .kg-bookmark-title {\n    font-size: 0.9em;\n}\n\n.kg-bookmark-card .kg-bookmark-description {\n    max-height: none;\n    margin-top: 0.3em;\n    font-size: 0.8em;\n}\n\n.kg-bookmark-card .kg-bookmark-metadata {\n    font-size: 0.8em;\n}\n\n.kg-bookmark-card .kg-bookmark-thumbnail img {\n    border-radius: 0 0.2em 0.2em 0;\n}\n\n/* Audio\n/* ---------------------------------------------------------- */\n\n.has-light-text .kg-audio-card button {\n    color: #fff;\n}\n\n.has-light-text .kg-audio-card .kg-audio-volume-slider {\n    color: rgb(255 255 255 / 0.3)\n}\n", ".pagination {\n    display: grid;\n    grid-template-columns: 1fr auto 1fr;\n    margin-top: 6.4rem;\n}\n\n.page-number {\n    grid-column-start: 2;\n}\n\n.older-posts {\n    text-align: right;\n}\n", ".gh-navigation {\n    display: grid;\n    grid-template-columns: 1fr auto 1fr;\n    column-gap: 2.4rem;\n    align-items: center;\n}\n\n.gh-navigation > div {\n    display: flex;\n    align-items: center;\n}\n\n.gh-navigation-next {\n    justify-content: flex-end;\n}\n\n.gh-navigation-link {\n    display: inline-flex;\n    align-items: center;\n}\n\n.gh-navigation-link svg {\n    width: 16px;\n    height: 16px;\n}\n\n.gh-navigation-previous svg {\n    margin-right: 0.4rem;\n}\n\n.gh-navigation-next svg {\n    margin-left: 0.4rem;\n}\n\n@media (max-width: 767px) {\n    .gh-navigation-hide {\n        display: none;\n    }\n}\n", ".gh-btn {\n    display: inline-flex;\n    gap: 0.4em;\n    align-items: center;\n    justify-content: center;\n    padding: calc(0.75em * var(--multiplier, 1)) calc(1.15em * var(--multiplier, 1));\n    font-size: 1.6rem;\n    font-weight: 700;\n    line-height: 1;\n    color: var(--color-white);\n    letter-spacing: inherit;\n    cursor: pointer;\n    background-color: var(--color-darker-gray);\n    border: 0;\n    border-radius: 100px;\n}\n\n.gh-btn:hover {\n    opacity: 0.95;\n}\n\n.gh-post-upgrade-cta .gh-btn {\n    line-height: inherit;\n}\n\n.gh-primary-btn {\n    background-color: var(--ghost-accent-color);\n}\n\n.gh-outline-btn {\n    color: var(--color-darker-gray);\n    text-decoration: none;\n    background-color: transparent;\n    border: 1px solid var(--color-light-gray);\n}\n\n.gh-outline-btn:hover {\n    border-color: var(--color-mid-gray);\n    opacity: 1;\n}\n\n.gh-icon-btn {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    width: 30px;\n    height: 30px;\n    padding: 0;\n    color: var(--darker-gray-color);\n    cursor: pointer;\n    background-color: transparent;\n    border: 0;\n    outline: none;\n}\n\n.gh-icon-btn svg {\n    width: 20px;\n    height: 20px;\n}\n\n.gh-text-btn {\n    display: inline-flex;\n    align-items: center;\n    justify-content: center;\n    padding: 0;\n    margin: 0;\n    color: var(--darker-gray-color);\n    letter-spacing: inherit;\n    cursor: pointer;\n    background-color: transparent;\n    border: 0;\n    outline: none;\n    will-change: opacity;\n}\n\n.gh-text-btn:hover {\n    opacity: 0.8;\n}\n", ".gh-foot {\n    padding-top: 8rem;\n    padding-bottom: 8rem;\n    color: var(--color-secondary-text);\n    white-space: nowrap;\n}\n\n.gh-foot-inner {\n    display: grid;\n    grid-template-columns: 1fr auto 1fr;\n    gap: 40px;\n    font-size: 1.3rem;\n}\n\n.no-menu .gh-foot-inner {\n    display: flex;\n    flex-direction: column;\n    gap: 24px;\n    align-items: center;\n}\n\n.gh-foot-menu .nav {\n    display: inline-flex;\n    flex-wrap: wrap;\n    row-gap: 4px;\n    align-items: center;\n    justify-content: center;\n    padding: 0;\n    margin: 0;\n    list-style: none;\n}\n\n.gh-foot-menu .nav li {\n    display: flex;\n    align-items: center;\n}\n\n.gh-foot-menu .nav li + li::before {\n    padding: 0 1.2rem;\n    font-size: 0.9rem;\n    line-height: 0;\n    content: \"•\";\n}\n\n.gh-powered-by {\n    text-align: right;\n}\n\n.gh-foot a {\n    color: var(--color-secondary-text);\n}\n\n.gh-foot a:hover {\n    color: var(--color-darker-gray);\n    opacity: 1;\n}\n\n@media (max-width: 767px) {\n    .gh-foot {\n        padding-top: 6.4rem;\n        padding-bottom: 12rem;\n    }\n\n    .gh-foot-inner {\n        grid-template-columns: 1fr;\n        text-align: center;\n    }\n\n    .gh-foot-menu .nav {\n        flex-direction: column;\n    }\n\n    .gh-foot-menu .nav li + li {\n        margin-top: 0.4rem;\n    }\n\n    .gh-foot-menu .nav li + li::before {\n        display: none;\n    }\n\n    .gh-powered-by {\n        text-align: center;\n    }\n}\n", ".pswp {\n    position: absolute;\n    top: 0;\n    left: 0;\n    z-index: 3999999;\n    display: none;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n    -ms-touch-action: none;\n    touch-action: none;\n    outline: none;\n    backface-visibility: hidden;\n    -webkit-text-size-adjust: 100%;\n}\n\n.pswp img {\n    max-width: none;\n}\n\n.pswp--animate_opacity {\n    opacity: 0.001;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    will-change: opacity;\n}\n\n.pswp--open {\n    display: block;\n}\n\n.pswp--zoom-allowed .pswp__img {\n    cursor: zoom-in;\n}\n\n.pswp--zoomed-in .pswp__img {\n    cursor: grab;\n}\n\n.pswp--dragging .pswp__img {\n    cursor: grabbing;\n}\n\n.pswp__bg {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background-color: rgba(0, 0, 0, 0.85);\n    opacity: 0;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    transform: translateZ(0);\n    backface-visibility: hidden;\n    will-change: opacity;\n}\n\n.pswp__scroll-wrap {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    overflow: hidden;\n}\n\n.pswp__container,\n.pswp__zoom-wrap {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    touch-action: none;\n    backface-visibility: hidden;\n}\n\n.pswp__container,\n.pswp__img {\n    user-select: none;\n    -webkit-tap-highlight-color: transparent;\n    -webkit-touch-callout: none;\n}\n\n.pswp__zoom-wrap {\n    position: absolute;\n    width: 100%;\n    transition: transform 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    transform-origin: left top;\n}\n\n.pswp--animated-in .pswp__bg,\n.pswp--animated-in .pswp__zoom-wrap {\n    transition: none;\n}\n\n.pswp__item {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    overflow: hidden;\n}\n\n.pswp__img {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: auto;\n    height: auto;\n}\n\n.pswp__img--placeholder {\n    backface-visibility: hidden;\n}\n\n.pswp__img--placeholder--blank {\n    background: var(--color-black);\n}\n\n.pswp--ie .pswp__img {\n    top: 0;\n    left: 0;\n    width: 100% !important;\n    height: auto !important;\n}\n\n.pswp__error-msg {\n    position: absolute;\n    top: 50%;\n    left: 0;\n    width: 100%;\n    margin-top: -8px;\n    font-size: 14px;\n    line-height: 16px;\n    color: var(--color-secondary-text);\n    text-align: center;\n}\n\n.pswp__error-msg a {\n    color: var(--color-secondary-text);\n    text-decoration: underline;\n}\n\n.pswp__button {\n    position: relative;\n    display: block;\n    float: right;\n    width: 44px;\n    height: 44px;\n    padding: 0;\n    margin: 0;\n    overflow: visible;\n    appearance: none;\n    cursor: pointer;\n    background: none;\n    border: 0;\n    box-shadow: none;\n    transition: opacity 0.2s;\n}\n\n.pswp__button:focus,\n.pswp__button:hover {\n    opacity: 1;\n}\n\n.pswp__button:active {\n    outline: none;\n    opacity: 0.9;\n}\n\n.pswp__button::-moz-focus-inner {\n    padding: 0;\n    border: 0;\n}\n\n.pswp__ui--over-close .pswp__button--close {\n    opacity: 1;\n}\n\n.pswp__button,\n.pswp__button--arrow--left::before,\n.pswp__button--arrow--right::before {\n    width: 44px;\n    height: 44px;\n    background: url(\"../images/default-skin.png\") 0 0 no-repeat;\n    background-size: 264px 88px;\n}\n\n@media (-webkit-min-device-pixel-ratio: 1.1), (-webkit-min-device-pixel-ratio: 1.09375), (min-resolution: 105dpi), (min-resolution: 1.1dppx) {\n    .pswp--svg .pswp__button,\n    .pswp--svg .pswp__button--arrow--left::before,\n    .pswp--svg .pswp__button--arrow--right::before {\n        background-image: url(\"../images/default-skin.svg\");\n    }\n\n    .pswp--svg .pswp__button--arrow--left,\n    .pswp--svg .pswp__button--arrow--right {\n        background: none;\n    }\n}\n\n.pswp__button--close {\n    background-position: 0 -44px;\n}\n\n.pswp__button--share {\n    background-position: -44px -44px;\n}\n\n.pswp__button--fs {\n    display: none;\n}\n\n.pswp--supports-fs .pswp__button--fs {\n    display: block;\n}\n\n.pswp--fs .pswp__button--fs {\n    background-position: -44px 0;\n}\n\n.pswp__button--zoom {\n    display: none;\n    background-position: -88px 0;\n}\n\n.pswp--zoom-allowed .pswp__button--zoom {\n    display: block;\n}\n\n.pswp--zoomed-in .pswp__button--zoom {\n    background-position: -132px 0;\n}\n\n.pswp--touch .pswp__button--arrow--left,\n.pswp--touch .pswp__button--arrow--right {\n    visibility: hidden;\n}\n\n.pswp__button--arrow--left,\n.pswp__button--arrow--right {\n    position: absolute;\n    top: 50%;\n    width: 70px;\n    height: 100px;\n    margin-top: -50px;\n    background: none;\n}\n\n.pswp__button--arrow--left {\n    left: 0;\n}\n\n.pswp__button--arrow--right {\n    right: 0;\n}\n\n.pswp__button--arrow--left::before,\n.pswp__button--arrow--right::before {\n    position: absolute;\n    top: 35px;\n    width: 32px;\n    height: 30px;\n    content: \"\";\n}\n\n.pswp__button--arrow--left::before {\n    left: 6px;\n    background-position: -138px -44px;\n}\n\n.pswp__button--arrow--right::before {\n    right: 6px;\n    background-position: -94px -44px;\n}\n\n.pswp__counter {\n    position: absolute;\n    top: 0;\n    left: 0;\n    height: 44px;\n    padding: 0 15px;\n    font-size: 11px;\n    font-weight: 700;\n    line-height: 44px;\n    color: var(--color-white);\n    user-select: none;\n}\n\n.pswp__caption {\n    position: absolute;\n    bottom: 0;\n    left: 0;\n    width: 100%;\n    min-height: 44px;\n}\n\n.pswp__caption__center {\n    max-width: 420px;\n    padding: 25px 15px 30px;\n    margin: 0 auto;\n    font-size: 11px;\n    line-height: 1.6;\n    color: var(--color-white);\n    text-align: center;\n}\n\n.pswp__caption__center .post-caption-title {\n    margin-bottom: 7px;\n    font-size: 15px;\n    font-weight: 500;\n    text-transform: uppercase;\n}\n\n.pswp__caption__center .post-caption-meta-item + .post-caption-meta-item::before {\n    padding: 0 4px;\n    content: \"\\02022\";\n}\n\n.pswp__caption--empty {\n    display: none;\n}\n\n.pswp__caption--fake {\n    visibility: hidden;\n}\n\n.pswp__preloader {\n    position: absolute;\n    top: 0;\n    left: 50%;\n    width: 44px;\n    height: 44px;\n    margin-left: -22px;\n    opacity: 0;\n    transition: opacity 0.25s ease-out;\n    direction: ltr;\n    will-change: opacity;\n}\n\n.pswp__preloader__icn {\n    width: 20px;\n    height: 20px;\n    margin: 12px;\n}\n\n.pswp__preloader--active {\n    opacity: 1;\n}\n\n.pswp__preloader--active .pswp__preloader__icn {\n    background: url(\"../images/preloader.gif\") 0 0 no-repeat;\n}\n\n.pswp--css_animation .pswp__preloader--active {\n    opacity: 1;\n}\n\n.pswp--css_animation .pswp__preloader--active .pswp__preloader__icn {\n    animation: clockwise 500ms linear infinite;\n}\n\n.pswp--css_animation .pswp__preloader--active .pswp__preloader__donut {\n    animation: donut-rotate 1000ms cubic-bezier(0.4, 0, 0.22, 1) infinite;\n}\n\n.pswp--css_animation .pswp__preloader__icn {\n    position: absolute;\n    top: 15px;\n    left: 15px;\n    width: 14px;\n    height: 14px;\n    margin: 0;\n    background: none;\n    opacity: 0.75;\n}\n\n.pswp--css_animation .pswp__preloader__cut {\n    position: relative;\n    width: 7px;\n    height: 14px;\n    overflow: hidden;\n}\n\n.pswp--css_animation .pswp__preloader__donut {\n    position: absolute;\n    top: 0;\n    left: 0;\n    box-sizing: border-box;\n    width: 14px;\n    height: 14px;\n    margin: 0;\n    background: none;\n    border: 2px solid var(--color-white);\n    border-bottom-color: transparent;\n    border-left-color: transparent;\n    border-radius: 50%;\n}\n\n@media screen and (max-width: 1024px) {\n    .pswp__preloader {\n        position: relative;\n        top: auto;\n        left: auto;\n        float: right;\n        margin: 0;\n    }\n}\n\n@keyframes clockwise {\n    0% {\n        transform: rotate(0deg);\n    }\n\n    100% {\n        transform: rotate(360deg);\n    }\n}\n\n@keyframes donut-rotate {\n    0% {\n        transform: rotate(0);\n    }\n\n    50% {\n        transform: rotate(-140deg);\n    }\n\n    100% {\n        transform: rotate(0);\n    }\n}\n\n.pswp__ui {\n    z-index: 1550;\n    visibility: visible;\n    opacity: 1;\n    -webkit-font-smoothing: auto;\n}\n\n.pswp__top-bar {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 44px;\n}\n\n.pswp__caption,\n.pswp__top-bar,\n.pswp--has_mouse .pswp__button--arrow--left,\n.pswp--has_mouse .pswp__button--arrow--right {\n    backface-visibility: hidden;\n    transition: opacity 333ms cubic-bezier(0.4, 0, 0.22, 1);\n    will-change: opacity;\n}\n\n.pswp--has_mouse .pswp__button--arrow--left,\n.pswp--has_mouse .pswp__button--arrow--right {\n    visibility: visible;\n}\n\n.pswp__ui--idle .pswp__top-bar {\n    opacity: 0;\n}\n\n.pswp__ui--idle .pswp__button--arrow--left,\n.pswp__ui--idle .pswp__button--arrow--right {\n    opacity: 0;\n}\n\n.pswp__ui--hidden .pswp__top-bar,\n.pswp__ui--hidden .pswp__caption,\n.pswp__ui--hidden .pswp__button--arrow--left,\n.pswp__ui--hidden .pswp__button--arrow--right {\n    opacity: 0.001;\n}\n\n.pswp__ui--one-slide .pswp__button--arrow--left,\n.pswp__ui--one-slide .pswp__button--arrow--right,\n.pswp__ui--one-slide .pswp__counter {\n    display: none;\n}\n\n.pswp__element--disabled {\n    display: none !important;\n}\n\n.pswp--minimal--dark .pswp__top-bar {\n    background: none;\n}\n", ":root {\n    --primary-color: var(--ghost-accent-color, #6c5ce7);\n    --primary-text-color: #333;\n    --secondary-text-color: #999;\n    --red-color: #dc3545;\n    --orange-color: #ffc107;\n    --green-color: #28a745;\n    --white-color: #fff;\n    --lighter-gray-color: #f7f7f7;\n    --light-gray-color: #f1f1f1;\n    --mid-gray-color: #e6e6e6;\n    --dark-gray-color: #1a1a1a;\n    --black-color: #000;\n    --animation-base: ease-in-out;\n    --h1-size: 3.8rem;\n    --header-spacing: 60px;\n    --dark-sky: #0a0312;\n}\n", "input[type=\"text\"],\ninput[type=\"password\"],\ninput[type=\"email\"],\ninput[type=\"search\"] {\n    width: 100%;\n    height: 50px;\n    padding: 0 15px;\n    font-size: 16px;\n    appearance: none;\n    border: 1px solid var(--mid-gray-color);\n    border-radius: 5px;\n    outline: none;\n}\n\ninput[type=\"text\"]:focus,\ninput[type=\"password\"]:focus,\ninput[type=\"email\"]:focus,\ninput[type=\"search\"]:focus {\n    border-color: var(--primary-color);\n}\n\n.form-wrapper {\n    position: relative;\n}\n\n.form-button {\n    position: absolute;\n    top: 5px;\n    right: 5px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 40px;\n    height: 40px;\n    padding: 0;\n    font-size: 20px;\n    color: var(--white-color);\n    cursor: pointer;\n    background-color: var(--primary-color);\n    border: 0;\n    border-radius: 4px;\n    outline: none;\n}\n", ".site {\n    display: flex;\n    flex-direction: column;\n    min-height: 100vh;\n}\n\n.site-content {\n    flex-grow: 1;\n    padding-top: 60px;\n    padding-bottom: 30px;\n}\n\n.page-template .site-content {\n    padding-block: 0;\n}\n\n@media (max-width: 767px) {\n    .site-content {\n        padding-bottom: 0;\n    }\n}\n", ".gh-head-logo {\n    letter-spacing: -0.01em;\n}\n\n.gh-head-menu,\n.gh-head-link {\n    font-size: 1.5rem;\n    font-weight: 500;\n}\n\n.gh-head-menu::after {\n    display: none;\n}\n", ".site-cover {\n    z-index: unset !important;\n    display: flex;\n    min-height: 500px;\n}\n\n.site-cover .search {\n    width: 400px;\n    margin: 30px auto 0;\n}\n\n.site-cover .search-field {\n    display: flex;\n    align-items: center;\n    height: 50px;\n    padding: 0 15px;\n    font-size: 16px;\n    color: darkgray;\n    cursor: pointer;\n    background-color: var(--white-color);\n    border-radius: 5px;\n}\n\n.cover-content {\n    position: relative;\n    z-index: 20;\n    padding: 30px;\n    margin: auto;\n    color: var(--white-color);\n    text-align: center;\n}\n\n.cover-description {\n    max-width: 720px;\n    margin: 0 auto;\n    font-family: var(--gh-font-heading, var(--font-sans));\n    font-size: 3.6rem;\n    font-weight: 600;\n    line-height: 1.4;\n    word-break: break-word;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .cover-description {\n    font-family: var(--gh-font-heading, var(--font-serif));\n    font-weight: 700;\n}\n\n@media (max-width: 460px) {\n    .site-cover .search {\n        width: 100%;\n    }\n}\n\n@media (max-width: 767px) {\n    .site-cover {\n        min-height: 400px;\n    }\n\n    .cover-description {\n        font-size: 24px;\n    }\n}\n", ".search {\n    position: relative;\n    text-align: left;\n}\n\n.search-button svg:nth-child(2) {\n    display: none;\n}\n\n.search-button-clear {\n    font-size: 18px;\n}\n\n.search-button-clear svg:nth-child(1) {\n    display: none;\n}\n\n.search-button-clear svg:nth-child(2) {\n    display: block;\n}\n\n.search-result {\n    position: absolute;\n    top: 100%;\n    width: 100%;\n    max-height: 50vh;\n    margin-top: 5px;\n    overflow: hidden;\n    overflow-y: scroll;\n    font-size: 15px;\n    color: var(--dark-gray-color);\n    background-color: var(--white-color);\n    border-radius: 5px;\n    box-shadow: 0 0 5px rgba(0, 0, 0, 0.15);\n    -webkit-overflow-scrolling: touch;\n}\n\n.search-result-row + .search-result-row {\n    border-top: 1px solid var(--mid-gray-color);\n}\n\n.search-result-row-link {\n    display: block;\n    padding: 15px 15px 12px;\n}\n\n.search-result-row-link:hover {\n    color: var(--dark-gray-color);\n    background-color: var(--light-gray-color);\n    opacity: 1;\n}\n\n.search-result-row-title {\n    font-weight: 600;\n    line-height: 1.25;\n}\n\n.search-result-row-excerpt {\n    display: block;\n    margin-top: 2px;\n    overflow: hidden;\n    font-size: 1.4rem;\n    color: var(--secondary-text-color);\n    text-overflow: ellipsis;\n    white-space: nowrap;\n}\n", "/* Search trigger button - matches Ghost search button */\n.page-search-trigger {\n    /* Inherits styling from .gh-search .gh-icon-btn */\n}\n\n/* Modal styles */\n.page-search-modal {\n    position: fixed;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    z-index: 9999;\n    backdrop-filter: blur(5px);\n    -webkit-backdrop-filter: blur(5px);\n}\n\n.page-search-modal-backdrop {\n    position: absolute;\n    top: 0;\n    left: 0;\n    width: 100%;\n    height: 100%;\n    background: rgba(0, 0, 0, 0.6);\n}\n\n.page-search-modal-content {\n    position: relative;\n    max-width: 600px;\n    margin: 80px auto 0;\n    background: var(--white-color);\n    border-radius: 8px;\n    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);\n    overflow: hidden;\n}\n\n.page-search-modal-header {\n    display: flex;\n    align-items: center;\n    padding: 20px;\n    border-bottom: 1px solid var(--light-gray-color);\n}\n\n.page-search-form {\n    flex: 1;\n    margin-right: 15px;\n}\n\n.form-wrapper {\n    display: flex;\n    align-items: center;\n    background: var(--light-gray-color);\n    border-radius: 5px;\n    padding: 0px;\n    height: 50px;\n}\n\n.page-search-field {\n    flex: 1;\n    border: none;\n    outline: none;\n    background: transparent;\n    font-size: 16px;\n    color: var(--dark-gray-color);\n}\n\n.page-search-field:focus {\n    outline: none;\n    box-shadow: none;\n}\n\n.page-search-button {\n    border: none;\n    background: transparent;\n    color: var(--dark-gray-color);\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    padding: 0;\n    padding-left: 15px;\n}\n\n.page-search-close {\n    border: none;\n    background: transparent;\n    color: var(--dark-gray-color);\n    cursor: pointer;\n    display: flex;\n    align-items: center;\n    padding: 8px;\n    border-radius: 4px;\n}\n\n.page-search-close:hover {\n    background: var(--light-gray-color);\n}\n\n.page-search-results {\n    max-height: 400px;\n    overflow-y: auto;\n    background: var(--white-color);\n}\n\n.page-search-result {\n    border-bottom: 1px solid var(--light-gray-color);\n    background: var(--white-color);\n}\n\n.page-search-result:last-child {\n    border-bottom: none;\n}\n\n.page-search-result-link {\n    display: block;\n    padding: 20px;\n    text-decoration: none;\n    color: inherit;\n    background: var(--white-color);\n}\n\n.page-search-result-link:hover {\n    background: var(--light-gray-color);\n}\n\n.page-search-result-title {\n    margin: 0 0 8px 0;\n    font-size: 18px;\n    font-weight: 600;\n    color: var(--dark-gray-color);\n}\n\n.page-search-result-excerpt {\n    margin: 0;\n    font-size: 14px;\n    color: var(--mid-gray-color);\n    line-height: 1.5;\n}\n\n.page-search-no-results {\n    padding: 40px 20px;\n    text-align: center;\n    color: var(--mid-gray-color);\n    font-style: italic;\n    background: var(--white-color);\n}\n\n.page-search mark {\n    background: var(--ghost-accent-color);\n    color: white;\n    padding: 2px 4px;\n    border-radius: 2px;\n}\n\n.page-search-modal input[type=text]:focus {\n    border-color: rgba(255, 255, 255, 0.5);\n}\n\n/* Dark mode */\n.darkMode .page-search-modal-content {\n    background: var(--white-color);\n}\n\n\n.darkMode .page-search-modal input[type=text]:focus {\n    border-color: rgba(0, 0, 0, 0.5);\n}\n\n.darkMode .page-search-modal-header {\n    border-bottom-color: rgba(255, 255, 255, 0.1);\n    background: var(--white-color);\n}\n\n.darkMode .form-wrapper {\n    background: rgba(255, 255, 255, 0.1);\n}\n\n.darkMode .page-search-field {\n    color: #000;\n}\n\n.darkMode .page-search-button,\n.darkMode .page-search-close {\n    color:  #000;\n}\n\n.darkMode .page-search-close:hover {\n    background: rgba(255, 255, 255, 0.1);\n}\n\n.darkMode .page-search-results {\n    background: var(--white-color);\n}\n\n.darkMode .page-search-result {\n    border-bottom-color: rgba(255, 255, 255, 0.1);\n    background: #fff;\n}\n\n.darkMode .page-search-result-link {\n    background:  #fff;\n}\n\n.darkMode .page-search-result-link:hover {\n    background: rgba(255, 255, 255, 0.466);\n}\n\n.darkMode .page-search-result-title {\n    color: #000;\n}\n\n.darkMode .page-search-result-excerpt {\n    color: #000;\n}\n\n.darkMode .page-search-no-results {\n    background: var(--dark-color);\n}\n", ".topic-feed {\n    display: grid;\n    grid-template-columns: 1fr 1fr;\n    gap: 80px 60px;\n}\n\n.topic-header {\n    display: flex;\n    padding-bottom: 12px;\n    margin-bottom: 20px;\n    border-bottom: 1px solid var(--mid-gray-color);\n}\n\n.topic-name {\n    margin-bottom: 0;\n    font-size: 2.2rem;\n    letter-spacing: -0.005em;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .topic-name {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.topic-count {\n    margin-left: 15px;\n    color: var(--secondary-text-color);\n}\n\n.topic-article-feed {\n    padding: 0;\n    margin: 0;\n    list-style-type: none;\n}\n\n.topic-article-title {\n    margin-bottom: 0;\n    font-family: var(--gh-font-body, var(--font-sans));\n    font-size: 1.5rem;\n    font-weight: 400;\n    line-height: 1.5;\n    letter-spacing: 0;\n    word-break: break-word;\n}\n\n.has-serif-body:not([class*=\" gh-font-body\"]):not([class^=\"gh-font-body\"]) .topic-article-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n.topic-article-link {\n    display: flex;\n    padding: 7px 0;\n}\n\n.topic-article-link svg {\n    width: 16px;\n    height: 16px;\n    margin: 3px 5px 0 -5px;\n}\n\n.topic-more {\n    display: inline-block;\n    margin-top: 20px;\n    font-weight: 700;\n    color: var(--primary-color);\n    letter-spacing: -0.005em;\n}\n\n@media (max-width: 767px) {\n    .topic-feed {\n        grid-template-columns: 1fr;\n    }\n}\n", ".term {\n    margin-top: 30px;\n    margin-bottom: 24px;\n}\n\n.term-name {\n    margin-bottom: 0;\n    font-size: 2.8rem;\n    letter-spacing: 0;\n}\n\n.term-description {\n    max-width: 560px;\n    margin-top: 12px;\n    line-height: 1.5;\n    color: var(--secondary-text-color);\n}\n\n.term-image {\n    width: 64px;\n    height: 64px;\n    margin-bottom: 12px;\n    border-radius: 50%;\n}\n", ".post-image-link:hover {\n    opacity: 1;\n}\n\n.post-title {\n    margin-bottom: 0;\n    font-size: 2.2rem;\n    font-weight: 700;\n    line-height: 1.2;\n    letter-spacing: -0.005em;\n    word-break: break-word;\n}\n\n.post-link:hover + .post-wrapper .post-title {\n    opacity: 0.8;\n}\n\n.post-title-link {\n    display: block;\n}\n\n.post-meta {\n    display: flex;\n    align-items: center;\n    margin-top: 16px;\n    font-size: 1.5rem;\n    line-height: 1;\n    color: var(--secondary-text-color);\n    letter-spacing: -0.005em;\n}\n\n.post-author {\n    position: relative;\n    z-index: 60;\n    display: flex;\n    align-items: center;\n    margin: 0 14px 0 4px;\n}\n\n.post-author-image-link {\n    position: relative;\n    display: block;\n    width: 40px;\n    height: 40px;\n    margin: 0 -6px;\n    overflow: hidden;\n    border-radius: 50%;\n}\n\n.post-author-image-link:first-child {\n    z-index: 10;\n}\n\n.post-author-image-link:nth-child(2) {\n    z-index: 9;\n}\n\n.post-author-image-link:nth-child(3) {\n    z-index: 8;\n}\n\n.post-author-image-link:nth-child(4) {\n    z-index: 7;\n}\n\n.post-author-image-link:nth-child(5) {\n    z-index: 6;\n}\n\n.post-author-image {\n    height: 100%;\n    border: 2px solid var(--white-color);\n    border-radius: 50%;\n    object-fit: cover;\n}\n\n.post-author-name {\n    margin-bottom: 6px;\n    font-weight: 600;\n}\n\n.post-author-name-link + .post-author-name-link::before {\n    content: \", \";\n}\n\n.post-meta-bottom {\n    display: flex;\n}\n\n.post-length::before {\n    padding: 0 4px;\n    content: \"—\";\n}\n\n.post-excerpt {\n    margin-top: 8px;\n    font-size: 1.6rem;\n    line-height: 1.5;\n    color: var(--secondary-text-color);\n    word-break: break-word;\n}\n\n.post-tags {\n    display: flex;\n    flex-wrap: wrap;\n    margin-top: 30px;\n}\n\n.post-tag {\n    display: inline-block;\n    padding: 6px 12px;\n    margin: 0 8px 8px 0;\n    font-size: 1.2rem;\n    font-weight: 600;\n    line-height: 1;\n    letter-spacing: -0.01em;\n    background-color: var(--white-color);\n    border: 1px solid var(--mid-gray-color);\n    border-radius: 15px;\n}\n", ".post-feed .post {\n    position: relative;\n    display: flex;\n    padding: 35px 0 33px;\n}\n\n.post-feed .post + .post {\n    border-top: 1px solid var(--mid-gray-color);\n}\n\n.post-feed .post-link:hover ~ .post-header .post-title-link {\n    color: var(--primary-color);\n}\n\n.post-feed .post-media {\n    flex-shrink: 0;\n    width: 160px;\n    margin-right: 28px;\n    margin-bottom: 0;\n}\n\n.post-feed .post.no-image .post-wrapper {\n    margin-left: 0;\n}\n\n@media (max-width: 767px) {\n    .post-feed .post {\n        display: block;\n    }\n\n    .post-feed .post-media {\n        width: auto;\n        margin-bottom: 30px;\n    }\n\n    .post-feed .post-wrapper {\n        margin-left: 0;\n    }\n}\n", ".pagination {\n    display: flex;\n    align-items: center;\n    margin-top: 48px;\n    margin-bottom: 32px;\n}\n\n.post-template .pagination {\n    gap: 32px;\n    align-items: flex-start;\n    margin-top: 64px;\n    font-size: 2.2rem;\n    line-height: 1.3;\n}\n\n.pagination-left,\n.pagination-right {\n    flex: 2;\n    font-weight: 700;\n}\n\n.pagination-right {\n    text-align: right;\n}\n\n.pagination-right .button-arrow-right {\n    justify-content: flex-end;\n}\n\n.page-number {\n    padding: 16px;\n    font-size: 1.3rem;\n    font-weight: 500;\n    line-height: 0;\n    color: var(--secondary-text-color);\n    border: 1px solid var(--mid-gray-color);\n    border-radius: 30px;\n}\n\n.pagination-label {\n    display: block;\n    margin-bottom: 8px;\n    font-size: 1.2rem;\n    font-weight: 600;\n    color: var(--secondary-text-color);\n    text-transform: uppercase;\n    word-break: break-word;\n}\n", ".featured-wrapper {\n    margin: 30px 0 90px;\n}\n\n.featured-wrapper .post-media {\n    margin-bottom: 20px;\n}\n\n.featured-wrapper .post-header {\n    margin-bottom: 20px;\n}\n\n.featured-wrapper .post-title {\n    font-size: 1.7rem;\n    font-weight: 600;\n}\n\n.featured-wrapper .post-tag {\n    margin: 0;\n}\n\n.featured-header {\n    margin-bottom: 45px;\n}\n\n.has-serif-title:not([class*=\" gh-font-heading\"]):not([class^=\"gh-font-heading\"]) .featured-title {\n    font-family: var(--gh-font-heading, var(--font-serif));\n}\n\n@media (max-width: 767px) {\n    .featured-wrapper {\n        margin-top: 0;\n        margin-bottom: 60px;\n    }\n\n    .featured-wrapper .post-header {\n        margin-bottom: 15px;\n    }\n\n    .featured-wrapper .owl .owl-prev,\n    .featured-wrapper .owl .owl-next {\n        top: -71px;\n    }\n\n    .featured-header {\n        margin-bottom: 30px;\n    }\n}\n", ".gh-article-image {\n    grid-column: main-start / main-end;\n}\n", ".related-posts {\n    margin-top: 48px;\n}\n\n.related-title {\n    margin-bottom: 0;\n    font-size: 14px;\n    color: var(--secondary-text-color);\n    text-transform: uppercase;\n    letter-spacing: 2px;\n}\n", ".comment-container {\n    margin-top: 48px;\n}\n", ".u-object-fit {\n    position: absolute;\n    width: 100%;\n    height: 100%;\n    object-fit: cover;\n}\n\n.u-permalink {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: 50;\n    outline: none;\n}\n\n.u-placeholder {\n    position: relative;\n    z-index: 10;\n    background-color: var(--light-gray-color);\n}\n\n.u-placeholder.same-height {\n    height: 0;\n}\n\n.u-placeholder.horizontal {\n    padding-bottom: 50%;\n}\n\n.u-placeholder.rectangle {\n    padding-bottom: 62.5%;\n}\n\n.u-placeholder.square {\n    padding-bottom: 100%;\n}\n\n.u-overlay {\n    position: relative;\n}\n\n.u-overlay::before {\n    position: absolute;\n    top: 0;\n    right: 0;\n    bottom: 0;\n    left: 0;\n    z-index: 10;\n    content: \"\";\n    background-color: var(--black-color);\n    transition: opacity 1s var(--animation-base);\n}\n\n.no-image.u-overlay::before {\n    background-color: var(--ghost-accent-color);\n}\n\n.u-overlay:not(.no-image).initialized::before {\n    opacity: 0.4;\n}\n\n@media (max-width: 575px) {\n    .hidden-xs {\n        display: none !important;\n    }\n}\n\n@media (min-width: 576px) and (max-width: 767px) {\n    .hidden-sm {\n        display: none !important;\n    }\n}\n\n@media (min-width: 768px) and (max-width: 991px) {\n    .hidden-md {\n        display: none !important;\n    }\n}\n\n@media (min-width: 992px) and (max-width: 1199px) {\n    .hidden-lg {\n        display: none !important;\n    }\n}\n\n@media (min-width: 1200px) {\n    .hidden-xl {\n        display: none !important;\n    }\n}\n", ".owl {\n    position: relative;\n    z-index: 1;\n    display: none;\n    width: 100%;\n    -webkit-tap-highlight-color: transparent;\n}\n\n.owl .owl-stage {\n    position: relative;\n    touch-action: pan-y;\n}\n\n.owl .owl-stage::after {\n    display: block;\n    height: 0;\n    clear: both;\n    line-height: 0;\n    visibility: hidden;\n    content: \".\";\n}\n\n.owl .owl-stage-outer {\n    position: relative;\n    overflow: hidden;\n    transform: translate3d(0, 0, 0);\n}\n\n.owl .owl-item {\n    position: relative;\n    float: left;\n    min-height: 1px;\n    transform: translateZ(0);\n    backface-visibility: hidden;\n    -webkit-tap-highlight-color: transparent;\n    -webkit-touch-callout: none;\n}\n\n.owl .owl-item > img {\n    display: block;\n    transform-style: preserve-3d;\n    width: 100%;\n}\n\n.owl .owl-nav.disabled,\n.owl .owl-dots.disabled {\n    display: none;\n}\n\n.owl .owl-prev,\n.owl .owl-next,\n.owl .owl-dot {\n    cursor: pointer;\n    user-select: none;\n}\n\n.owl .owl-prev,\n.owl .owl-next {\n    position: absolute;\n    top: -86px;\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 30px;\n    height: 30px;\n    padding: 0;\n    font-size: 18px;\n    color: var(--dark-gray-color);\n    text-align: center;\n    background-color: var(--white-color);\n    border: 1px solid var(--mid-gray-color);\n    border-radius: 3px;\n    outline: none;\n    transition: color 0.3s var(--animation-base);\n}\n\n.owl .owl-prev.disabled,\n.owl .owl-next.disabled {\n    color: var(--secondary-text-color);\n    cursor: default;\n}\n\n.owl .owl-prev {\n    right: 34px;\n}\n\n.owl .owl-next {\n    right: 0;\n}\n\n.owl .owl-dots {\n    display: flex;\n    justify-content: center;\n    margin-top: 20px;\n}\n\n.owl .owl-dot {\n    display: flex;\n    align-items: center;\n    justify-content: center;\n    width: 20px;\n    height: 20px;\n    padding: 0;\n    border: 0;\n    outline: none;\n}\n\n.owl .owl-dot span {\n    width: 8px;\n    height: 8px;\n    background-color: var(--mid-gray-color);\n    border-radius: 50%;\n}\n\n.owl .owl-dot.active span {\n    background-color: var(--black-color);\n}\n\n.owl.owl-loaded {\n    display: block;\n}\n\n.owl.owl-loading {\n    display: block;\n    opacity: 0;\n}\n\n.owl.owl-hidden {\n    opacity: 0;\n}\n\n.owl.owl-refresh .owl-item {\n    visibility: hidden;\n}\n\n.owl.owl-drag .owl-item {\n    user-select: none;\n}\n\n.owl.owl-grab {\n    cursor: move;\n}\n\n.no-js .owl {\n    display: block;\n}\n", "/* dark mode */\nbody.darkMode {\nbackground-color:hsla(265,70%,3%,1);\n/* background-image:\nradial-gradient(at 98% 26%, hsla(272,50%,10%,1) 0px, transparent 50%),\nradial-gradient(at 4% 22%, hsla(272,50%,10%,1) 0px, transparent 50%);\n*/\nbackground-image: url(/assets/images/gradient.jpg);\nbackground-size: 120% 120%;\nbackground-repeat: no-repeat;\nbackground-position: 50% 50%;\ncolor: var(--white-color);\nbackground-attachment: fixed;\n}\n\n.darkMode .gh-head {\nbackground: transparent;\ncolor: var(--white-color);\n}\n\n.darkMode a {\ncolor: var(--white-color);\t\n}\n\n.darkMode h1, .darkMode h2, .darkMode h3, .darkMode h4, .darkMode h5, .darkMode h6 {\ncolor: var(--white-color);\n}\n\n\n.darkMode .kg-callout-card-grey {\n    background: #77797e;\n}\n\n.darkMode .kg-callout-card-white {\n\tcolor: var(--black-color);\n}\n\n.darkMode .kg-callout-card-blue {\n    background: #8492c3;\n    color: var(--black-color);\n}\n\n.darkMode .kg-callout-card-green {\n    background: #84c3b0;\n    color: var(--black-color);\n}\n\n.darkMode .kg-callout-card-yellow {\n    background: #c3bc84;\n    color: var(--black-color);\n}\n\n.darkMode .kg-callout-card-red {\n    background: #c39184;\n    color: var(--black-color);\n}\n\n.darkMode .kg-callout-card-pink {\n    background: #c38484;\n    color: var(--black-color);\n}\n\n.darkMode .kg-callout-card-purple {\n    background: #9d84c3;\n    color: var(--black-color);\n}\n\nbody.darkMode .gh-foot a:hover {\n    color: var(--color-white);\n    opacity: 0.6;\n}\n\nbody.darkMode .gh-foot-menu .nav li+li:before {\n  display: none;\n}\n\n\n/* wide template */\n\nbody.wideTemplate .gh-canvas {\n    --content-width: min(70vw, 1260px);\n}\n@media (max-width: 1024px) {\n\tbody.wideTemplate .gh-canvas {\n    --content-width: 100vw;\n}\t\n}\n\n\n/* Global Style changes */\n* {\n\ttransition: all 0.2s ease;\n}\n\n.gh-foot-inner {\n  display: block;\n  text-align: center;\n}\n\n.gh-copyright {\n  margin-top: 30px;\n}\n\n.kg-toggle-card {\n    border-radius: 12px !important;\n}\n\n\n/* glass elements */\n\n.glass-button {\n  position: relative;\n  padding: 16px 32px;\n  background: rgba(255, 255, 255, 0.03);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n  border-radius: 16px !important;\n  color: white;\n  font-size: 18px;\n  font-weight: 500;\n  text-decoration: none !important;\n  cursor: pointer;\n  backdrop-filter: blur(7px);\n  -webkit-backdrop-filter: blur(7px);\n  transition: all 0.3s ease;\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.3);\n  overflow: hidden !important;\n  display: inline-block;\n  min-height: 10px;\n}\n\n.glass-button::before {\n  opacity: 0;\n  content: \"\";\n  position: absolute;\n  top: 0;\n  left: -100%;\n  width: 100%;\n  height: 100%;\n  background: linear-gradient(\n    90deg,\n    transparent,\n    rgba(255, 255, 255, 0.2),\n    transparent\n  );\n  transition: left 0.5s ease;\n}\n\n.glass-button:hover::before {\n  opacity: 1;\n}\n\n.glass-button:hover {\n  background: rgba(255, 255, 255, 0.07);\n  border-color: rgba(255, 255, 255, 0.3);\n  transform: translateY(-8px) !important;\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),\n    inset 0 1px 0 rgba(255, 255, 255, 0.4);\n}\n\n.glass-button:hover::before {\n  left: 100%;\n}\n\n.glass-button:active {\n  transform: translateY(0);\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),\n    inset 0 1px 0 rgba(255, 255, 255, 0.3);\n}\n\n/* Additional demo buttons */\n.button-container {\n  display: flex;\n  flex-direction: column;\n  gap: 20px;\n  align-items: center;\n  padding: 500px 0;\n}\n\n.glass-button.secondary {\n  background: rgba(255, 255, 255, 0.05);\n  border: 1px solid rgba(255, 255, 255, 0.15);\n}\n\n.glass-button.secondary:hover {\n  background: rgba(255, 255, 255, 0.1);\n  border-color: rgba(255, 255, 255, 0.25);\n}\n\n\n\n\n/* Embla Slider Styles */\n\n *,\n    *::before,\n    *::after {\n      box-sizing: inherit;\n    }\n    /* Theme Variables */\n    .theme-light {\n      --brand-primary: rgb(47, 112, 193);\n      --brand-secondary: rgb(116, 97, 195);\n      --brand-alternative: rgb(19, 120, 134);\n      --background-site: rgb(249, 249, 249);\n      --background-code: rgb(244, 244, 244);\n      --text-body: rgb(54, 49, 61);\n      --text-comment: rgb(99, 94, 105);\n      --text-high-contrast: rgb(49, 49, 49);\n      --text-medium-contrast: rgb(99, 94, 105);\n      --text-low-contrast: rgb(116, 109, 118);\n      --detail-high-contrast: rgb(192, 192, 192);\n      --detail-medium-contrast: rgb(234, 234, 234);\n      --detail-low-contrast: rgb(240, 240, 242);\n      --admonition-note: rgb(46, 109, 188);\n      --admonition-warning: rgb(255, 196, 9);\n      --admonition-danger: rgb(220, 38, 38);\n      --brand-primary-rgb-value: 47, 112, 193;\n      --brand-secondary-rgb-value: 116, 97, 195;\n      --brand-alternative-rgb-value: 19, 120, 134;\n      --background-site-rgb-value: 249, 249, 249;\n      --background-code-rgb-value: 244, 244, 244;\n      --text-body-rgb-value: 54, 49, 61;\n      --text-comment-rgb-value: 99, 94, 105;\n      --text-high-contrast-rgb-value: 49, 49, 49;\n      --text-medium-contrast-rgb-value: 99, 94, 105;\n      --text-low-contrast-rgb-value: 116, 109, 118;\n      --detail-high-contrast-rgb-value: 192, 192, 192;\n      --detail-medium-contrast-rgb-value: 234, 234, 234;\n      --detail-low-contrast-rgb-value: 240, 240, 242;\n      --admonition-note-rgb-value: 46, 109, 188;\n      --admonition-warning-rgb-value: 255, 196, 9;\n      --admonition-danger-rgb-value: 220, 38, 38;\n    }\n    .theme-dark {\n      --brand-primary: rgb(138, 180, 248);\n      --brand-secondary: rgb(193, 168, 226);\n      --brand-alternative: rgb(136, 186, 191);\n      --background-site: rgb(0, 0, 0);\n      --background-code: rgb(12, 12, 12);\n      --text-body: rgb(222, 222, 222);\n      --text-comment: rgb(170, 170, 170);\n      --text-high-contrast: rgb(230, 230, 230);\n      --text-medium-contrast: rgb(202, 202, 202);\n      --text-low-contrast: rgb(170, 170, 170);\n      --detail-high-contrast: rgb(101, 101, 101);\n      --detail-medium-contrast: rgb(25, 25, 25);\n      --detail-low-contrast: rgb(21, 21, 21);\n      --admonition-note: rgb(138, 180, 248);\n      --admonition-warning: rgb(253, 186, 116);\n      --admonition-danger: rgb(220, 38, 38);\n      --brand-primary-rgb-value: 138, 180, 248;\n      --brand-secondary-rgb-value: 193, 168, 226;\n      --brand-alternative-rgb-value: 136, 186, 191;\n      --background-site-rgb-value: 0, 0, 0;\n      --background-code-rgb-value: 12, 12, 12;\n      --text-body-rgb-value: 222, 222, 222;\n      --text-comment-rgb-value: 170, 170, 170;\n      --text-high-contrast-rgb-value: 230, 230, 230;\n      --text-medium-contrast-rgb-value: 202, 202, 202;\n      --text-low-contrast-rgb-value: 170, 170, 170;\n      --detail-high-contrast-rgb-value: 101, 101, 101;\n      --detail-medium-contrast-rgb-value: 25, 25, 25;\n      --detail-low-contrast-rgb-value: 21, 21, 21;\n      --admonition-note-rgb-value: 138, 180, 248;\n      --admonition-warning-rgb-value: 253, 186, 116;\n      --admonition-danger-rgb-value: 220, 38, 38;\n    }\n"]}