.post-image-link:hover {
    opacity: 1;
}

.post-title {
    margin-bottom: 0;
    font-size: 2.2rem;
    font-weight: 700;
    line-height: 1.2;
    letter-spacing: -0.005em;
    word-break: break-word;
}

.post-link:hover + .post-wrapper .post-title {
    opacity: 0.8;
}

.post-title-link {
    display: block;
}

.post-meta {
    display: flex;
    align-items: center;
    margin-top: 16px;
    font-size: 1.5rem;
    line-height: 1;
    color: var(--secondary-text-color);
    letter-spacing: -0.005em;
}

.post-author {
    position: relative;
    z-index: 60;
    display: flex;
    align-items: center;
    margin: 0 14px 0 4px;
}

.post-author-image-link {
    position: relative;
    display: block;
    width: 40px;
    height: 40px;
    margin: 0 -6px;
    overflow: hidden;
    border-radius: 50%;
}

.post-author-image-link:first-child {
    z-index: 10;
}

.post-author-image-link:nth-child(2) {
    z-index: 9;
}

.post-author-image-link:nth-child(3) {
    z-index: 8;
}

.post-author-image-link:nth-child(4) {
    z-index: 7;
}

.post-author-image-link:nth-child(5) {
    z-index: 6;
}

.post-author-image {
    height: 100%;
    border: 2px solid var(--white-color);
    border-radius: 50%;
    object-fit: cover;
}

.post-author-name {
    margin-bottom: 6px;
    font-weight: 600;
}

.post-author-name-link + .post-author-name-link::before {
    content: ", ";
}

.post-meta-bottom {
    display: flex;
}

.post-length::before {
    padding: 0 4px;
    content: "—";
}

.post-excerpt {
    margin-top: 8px;
    font-size: 1.6rem;
    line-height: 1.5;
    color: var(--secondary-text-color);
    word-break: break-word;
}

.post-tags {
    display: flex;
    flex-wrap: wrap;
    margin-top: 30px;
}

.post-tag {
    display: inline-block;
    padding: 6px 12px;
    margin: 0 8px 8px 0;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1;
    letter-spacing: -0.01em;
    background-color: var(--white-color);
    border: 1px solid var(--mid-gray-color);
    border-radius: 15px;
}
