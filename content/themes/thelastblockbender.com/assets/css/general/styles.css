/* dark mode */
body.darkMode {
background-color:hsla(265,70%,3%,1);
/* background-image:
radial-gradient(at 98% 26%, hsla(272,50%,10%,1) 0px, transparent 50%),
radial-gradient(at 4% 22%, hsla(272,50%,10%,1) 0px, transparent 50%);
*/
background-image: url(/assets/images/gradient.jpg);
background-size: 120% 120%;
background-repeat: no-repeat;
background-position: 50% 50%;
color: var(--white-color);
background-attachment: fixed;
}

.darkMode .gh-head {
background: transparent;
color: var(--white-color);
}

.darkMode a {
color: var(--white-color);	
}

.darkMode h1, .darkMode h2, .darkMode h3, .darkMode h4, .darkMode h5, .darkMode h6 {
color: var(--white-color);
}


.darkMode .kg-callout-card-grey {
    background: #77797e;
}

.darkMode .kg-callout-card-white {
	color: var(--black-color);
}

.darkMode .kg-callout-card-blue {
    background: #8492c3;
    color: var(--black-color);
}

.darkMode .kg-callout-card-green {
    background: #84c3b0;
    color: var(--black-color);
}

.darkMode .kg-callout-card-yellow {
    background: #c3bc84;
    color: var(--black-color);
}

.darkMode .kg-callout-card-red {
    background: #c39184;
    color: var(--black-color);
}

.darkMode .kg-callout-card-pink {
    background: #c38484;
    color: var(--black-color);
}

.darkMode .kg-callout-card-purple {
    background: #9d84c3;
    color: var(--black-color);
}

body.darkMode .gh-foot a:hover {
    color: var(--color-white);
    opacity: 0.6;
}

body.darkMode .gh-foot-menu .nav li+li:before {
  display: none;
}


/* wide template */

body.wideTemplate .gh-canvas {
    --content-width: min(70vw, 1260px);
}
@media (max-width: 1024px) {
	body.wideTemplate .gh-canvas {
    --content-width: 100vw;
}	
}


/* Global Style changes */
* {
	transition: all 0.2s ease;
}

.gh-foot-inner {
  display: block;
  text-align: center;
}

.gh-copyright {
  margin-top: 30px;
}

.kg-toggle-card {
    border-radius: 12px !important;
}


/* glass elements */

.glass-button {
  position: relative;
  padding: 16px 32px;
  background: rgba(255, 255, 255, 0.03);
  border: 1px solid rgba(255, 255, 255, 0.15);
  border-radius: 16px !important;
  color: white;
  font-size: 18px;
  font-weight: 500;
  text-decoration: none !important;
  cursor: pointer;
  backdrop-filter: blur(7px);
  -webkit-backdrop-filter: blur(7px);
  transition: all 0.3s ease;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden !important;
  display: inline-block;
  min-height: 10px;
}

.glass-button::before {
  opacity: 0;
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  transition: left 0.5s ease;
}

.glass-button:hover::before {
  opacity: 1;
}

.glass-button:hover {
  background: rgba(255, 255, 255, 0.07);
  border-color: rgba(255, 255, 255, 0.3);
  transform: translateY(-8px) !important;
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15),
    inset 0 1px 0 rgba(255, 255, 255, 0.4);
}

.glass-button:hover::before {
  left: 100%;
}

.glass-button:active {
  transform: translateY(0);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

/* Additional demo buttons */
.button-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
  align-items: center;
  padding: 500px 0;
}

.glass-button.secondary {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid rgba(255, 255, 255, 0.15);
}

.glass-button.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.25);
}




/* Embla Slider Styles */

 *,
    *::before,
    *::after {
      box-sizing: inherit;
    }
    /* Theme Variables */
    .theme-light {
      --brand-primary: rgb(47, 112, 193);
      --brand-secondary: rgb(116, 97, 195);
      --brand-alternative: rgb(19, 120, 134);
      --background-site: rgb(249, 249, 249);
      --background-code: rgb(244, 244, 244);
      --text-body: rgb(54, 49, 61);
      --text-comment: rgb(99, 94, 105);
      --text-high-contrast: rgb(49, 49, 49);
      --text-medium-contrast: rgb(99, 94, 105);
      --text-low-contrast: rgb(116, 109, 118);
      --detail-high-contrast: rgb(192, 192, 192);
      --detail-medium-contrast: rgb(234, 234, 234);
      --detail-low-contrast: rgb(240, 240, 242);
      --admonition-note: rgb(46, 109, 188);
      --admonition-warning: rgb(255, 196, 9);
      --admonition-danger: rgb(220, 38, 38);
      --brand-primary-rgb-value: 47, 112, 193;
      --brand-secondary-rgb-value: 116, 97, 195;
      --brand-alternative-rgb-value: 19, 120, 134;
      --background-site-rgb-value: 249, 249, 249;
      --background-code-rgb-value: 244, 244, 244;
      --text-body-rgb-value: 54, 49, 61;
      --text-comment-rgb-value: 99, 94, 105;
      --text-high-contrast-rgb-value: 49, 49, 49;
      --text-medium-contrast-rgb-value: 99, 94, 105;
      --text-low-contrast-rgb-value: 116, 109, 118;
      --detail-high-contrast-rgb-value: 192, 192, 192;
      --detail-medium-contrast-rgb-value: 234, 234, 234;
      --detail-low-contrast-rgb-value: 240, 240, 242;
      --admonition-note-rgb-value: 46, 109, 188;
      --admonition-warning-rgb-value: 255, 196, 9;
      --admonition-danger-rgb-value: 220, 38, 38;
    }
    .theme-dark {
      --brand-primary: rgb(138, 180, 248);
      --brand-secondary: rgb(193, 168, 226);
      --brand-alternative: rgb(136, 186, 191);
      --background-site: rgb(0, 0, 0);
      --background-code: rgb(12, 12, 12);
      --text-body: rgb(222, 222, 222);
      --text-comment: rgb(170, 170, 170);
      --text-high-contrast: rgb(230, 230, 230);
      --text-medium-contrast: rgb(202, 202, 202);
      --text-low-contrast: rgb(170, 170, 170);
      --detail-high-contrast: rgb(101, 101, 101);
      --detail-medium-contrast: rgb(25, 25, 25);
      --detail-low-contrast: rgb(21, 21, 21);
      --admonition-note: rgb(138, 180, 248);
      --admonition-warning: rgb(253, 186, 116);
      --admonition-danger: rgb(220, 38, 38);
      --brand-primary-rgb-value: 138, 180, 248;
      --brand-secondary-rgb-value: 193, 168, 226;
      --brand-alternative-rgb-value: 136, 186, 191;
      --background-site-rgb-value: 0, 0, 0;
      --background-code-rgb-value: 12, 12, 12;
      --text-body-rgb-value: 222, 222, 222;
      --text-comment-rgb-value: 170, 170, 170;
      --text-high-contrast-rgb-value: 230, 230, 230;
      --text-medium-contrast-rgb-value: 202, 202, 202;
      --text-low-contrast-rgb-value: 170, 170, 170;
      --detail-high-contrast-rgb-value: 101, 101, 101;
      --detail-medium-contrast-rgb-value: 25, 25, 25;
      --detail-low-contrast-rgb-value: 21, 21, 21;
      --admonition-note-rgb-value: 138, 180, 248;
      --admonition-warning-rgb-value: 253, 186, 116;
      --admonition-danger-rgb-value: 220, 38, 38;
    }
