input[type="text"],
input[type="password"],
input[type="email"],
input[type="search"] {
    width: 100%;
    height: 50px;
    padding: 0 15px;
    font-size: 16px;
    appearance: none;
    border: 1px solid var(--mid-gray-color);
    border-radius: 5px;
    outline: none;
}

input[type="text"]:focus,
input[type="password"]:focus,
input[type="email"]:focus,
input[type="search"]:focus {
    border-color: var(--primary-color);
}

.form-wrapper {
    position: relative;
}

.form-button {
    position: absolute;
    top: 5px;
    right: 5px;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    padding: 0;
    font-size: 20px;
    color: var(--white-color);
    cursor: pointer;
    background-color: var(--primary-color);
    border: 0;
    border-radius: 4px;
    outline: none;
}
