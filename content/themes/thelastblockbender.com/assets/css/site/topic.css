.topic-feed {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 80px 60px;
}

.topic-header {
    display: flex;
    padding-bottom: 12px;
    margin-bottom: 20px;
    border-bottom: 1px solid var(--mid-gray-color);
}

.topic-name {
    margin-bottom: 0;
    font-size: 2.2rem;
    letter-spacing: -0.005em;
}

.has-serif-title:not([class*=" gh-font-heading"]):not([class^="gh-font-heading"]) .topic-name {
    font-family: var(--gh-font-heading, var(--font-serif));
}

.topic-count {
    margin-left: 15px;
    color: var(--secondary-text-color);
}

.topic-article-feed {
    padding: 0;
    margin: 0;
    list-style-type: none;
}

.topic-article-title {
    margin-bottom: 0;
    font-family: var(--gh-font-body, var(--font-sans));
    font-size: 1.5rem;
    font-weight: 400;
    line-height: 1.5;
    letter-spacing: 0;
    word-break: break-word;
}

.has-serif-body:not([class*=" gh-font-body"]):not([class^="gh-font-body"]) .topic-article-title {
    font-family: var(--gh-font-heading, var(--font-serif));
}

.topic-article-link {
    display: flex;
    padding: 7px 0;
}

.topic-article-link svg {
    width: 16px;
    height: 16px;
    margin: 3px 5px 0 -5px;
}

.topic-more {
    display: inline-block;
    margin-top: 20px;
    font-weight: 700;
    color: var(--primary-color);
    letter-spacing: -0.005em;
}

@media (max-width: 767px) {
    .topic-feed {
        grid-template-columns: 1fr;
    }
}
