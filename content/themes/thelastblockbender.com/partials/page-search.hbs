<button class="gh-search gh-icon-btn page-search-trigger" aria-label="Search pages">
    {{> "icons/search"}}
</button>

<!-- Search Modal -->
<div class="page-search-modal" style="display: none;">
    <div class="page-search-modal-backdrop"></div>
    <div class="page-search-modal-content">
        <div class="page-search-modal-header">
            <form class="page-search-form">
                <div class="form-wrapper">
                    <input type="text" class="page-search-field" placeholder="Search pages..." autocomplete="off" autofocus>
                    <button class="page-search-button" type="submit">
                        {{> "icons/search"}}
                    </button>
                </div>
            </form>
            <button class="page-search-close" aria-label="Close search">
                {{> "icons/close"}}
            </button>
        </div>
        <div class="page-search-results"></div>
    </div>
</div>

<script>
function initPageSearch() {
    const searchTrigger = document.querySelector('.page-search-trigger');
    const searchModal = document.querySelector('.page-search-modal');
    const searchForm = document.querySelector('.page-search-form');
    const searchField = document.querySelector('.page-search-field');
    const searchResults = document.querySelector('.page-search-results');
    const searchClose = document.querySelector('.page-search-close');
    const modalBackdrop = document.querySelector('.page-search-modal-backdrop');
    
    // Exit if elements don't exist
    if (!searchTrigger || !searchModal) {
        return;
    }
    
    let searchTimeout;

    // Get the API key
    const apiKey = window.ghost && window.ghost.url ? 
        new URLSearchParams(window.location.search).get('key') || 
        '{{@site.content_api_key}}' || 
        '9a41b1e7087364643a05c513cf' : 
        'a0e8ebac05f88aa1a2dfd76548';

    // Open modal
    searchTrigger.addEventListener('click', function(e) {
        e.preventDefault();
        e.stopPropagation();
        searchModal.style.display = 'block';
        document.body.style.overflow = 'hidden';
        setTimeout(() => searchField.focus(), 100);
    });

    // Close modal
    function closeModal() {
        searchModal.style.display = 'none';
        document.body.style.overflow = '';
        searchField.value = '';
        searchResults.innerHTML = '';
    }

    if (searchClose) {
        searchClose.addEventListener('click', closeModal);
    }
    
    if (modalBackdrop) {
        modalBackdrop.addEventListener('click', closeModal);
    }

    // Close on escape key
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Escape' && searchModal.style.display === 'block') {
            closeModal();
        }
    });

    function performSearch(query) {
        if (query.length < 2) {
            searchResults.innerHTML = '';
            return;
        }

        const apiUrl = `/ghost/api/content/pages/?key=${apiKey}&fields=title,url,excerpt,plaintext&limit=all`;
        
        fetch(apiUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data.pages) {
                    throw new Error('No pages data in response');
                }
                
                const results = data.pages.filter(page => {
                    const searchText = query.toLowerCase();
                    return page.title.toLowerCase().includes(searchText) ||
                           (page.excerpt && page.excerpt.toLowerCase().includes(searchText)) ||
                           (page.plaintext && page.plaintext.toLowerCase().includes(searchText));
                });

                displayResults(results, query);
            })
            .catch(error => {
                console.error('Search error:', error);
                searchResults.innerHTML = '<div class="page-search-no-results">Search temporarily unavailable</div>';
            });
    }

    function displayResults(results, query) {
        if (results.length === 0) {
            searchResults.innerHTML = '<div class="page-search-no-results">No pages found</div>';
        } else {
            const resultsHtml = results.map(page => {
                let excerpt = '';
                if (page.excerpt) {
                    excerpt = page.excerpt.length > 160 ? 
                        page.excerpt.substring(0, 160) + '...' : 
                        page.excerpt;
                } else if (page.plaintext) {
                    excerpt = page.plaintext.length > 160 ? 
                        page.plaintext.substring(0, 160) + '...' : 
                        page.plaintext;
                }
                
                return `
                    <div class="page-search-result">
                        <a href="${page.url}" class="page-search-result-link">
                            <h4 class="page-search-result-title">${highlightText(page.title, query)}</h4>
                            ${excerpt ? `<p class="page-search-result-excerpt">${highlightText(excerpt, query)}</p>` : ''}
                        </a>
                    </div>
                `;
            }).join('');
            searchResults.innerHTML = resultsHtml;
        }
    }

    function highlightText(text, query) {
        const regex = new RegExp(`(${query})`, 'gi');
        return text.replace(regex, '<mark>$1</mark>');
    }

    if (searchField) {
        searchField.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            const query = this.value.trim();
            
            searchTimeout = setTimeout(() => {
                performSearch(query);
            }, 300);
        });
    }

    if (searchForm) {
        searchForm.addEventListener('submit', function(e) {
            e.preventDefault();
            performSearch(searchField.value.trim());
        });
    }
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initPageSearch);
} else {
    initPageSearch();
}
</script>
