
<div id="onlineCount"></div>

<script>
    const SERVER_IP = 'play.blockbender.com';

    async function fetchMinecraftData() {
        const response = await fetch(`https://api.mcsrvstat.us/3/${SERVER_IP}`);
        return response.json();
    }

    async function updateWidget() {
        const onlineCountEl = document.getElementById('onlineCount');
        const maxCountEl = document.getElementById('maxCount');

        onlineCountEl.textContent = '...';

        try {
            const data = await fetchMinecraftData();
            const onlinePlayers = data.players ? data.players.online : 0;
            const maxPlayers = data.players ? data.players.max : 0;

            onlineCountEl.textContent = onlinePlayers + " Online";
        } catch (error) {
            onlineCountEl.textContent = "Error";
            maxCountEl.textContent = "Error";
        }
    }

    updateWidget();
</script>