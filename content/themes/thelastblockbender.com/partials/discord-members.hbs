
<div id="onlineCount"></div>
<div id="totalCount"></div>

<script>
    const INVITE_CODE = 'FJet9budEs';

    async function fetchDiscordData() {
        const response = await fetch(`https://discord.com/api/invites/${INVITE_CODE}?with_counts=true`);
        return response.json();
    }

    async function updateWidget() {
        const onlineCountEl = document.getElementById('onlineCount');
        const totalCountEl = document.getElementById('totalCount');

        onlineCountEl.textContent = '...';
        totalCountEl.textContent = '...';

        const data = await fetchDiscordData();
        const onlineMembers = data.approximate_presence_count || 0;
        const totalMembers = data.approximate_member_count || 0;

        onlineCountEl.textContent = onlineMembers + " Online";
        totalCountEl.textContent = totalMembers + " Members";
    }

    updateWidget();
</script>