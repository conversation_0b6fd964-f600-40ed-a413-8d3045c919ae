{{!< default}}

{{#post}}

<main class="site-main">
    <article class="{{post_class}}">

        {{#match @page.show_title_and_feature_image}}
            <header class="gh-article-header gh-canvas">
                <h1 class="gh-article-title">{{title}}</h1>
                {{> "post-image"}}
            </header>
        {{/match}}

        <div class="post-content left gh-content gh-canvas">
            {{content}}
        </div>

        <div class="post-content right gh-content gh-canvas">

        </div>

    </article>
</main>

<style>

article.post {
    text-align: center;
}

.post-content.left {
    width: calc(50% - 15px);
    max-width: 620px;
    display: inline-block;
    vertical-align: top;
    padding-right: 15px;
    text-align: left;
    box-sizing: border-box;
}

.post-content.right {
    width: (50% - 15px);
    max-width: 620px;
    display: inline-block;
    vertical-align: top;
    padding-left: 15px;
    text-align: left;
    box-sizing: border-box;
}


    .post-content.right .glass-button {
        width: 100%;
        margin-left: 10%;
        text-align: center;
    }

@media (max-width: 900px) {

    .post-content.left {
        width: 100%;
        display: block;
        max-width: none;
        vertical-align: top;
        padding-right: 0px;
        text-align: left;
        padding: 0px 30px;
    }

    .post-content.right {
        width: 100%;
        display: block;
        max-width: none;
        vertical-align: top;
        padding-left: 0px;
        text-align: left;
        padding: 0px 30px;
    }
    .post-content.right .glass-button {
        width: 80%;
        margin-left: 10%;
        margin-bottom: 15px;
        text-align: center;
    }

</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Find the left and right containers
    const leftContainer = document.querySelector('.post-content.left');
    const rightContainer = document.querySelector('.post-content.right');

    // Check if both containers exist
    if (leftContainer && rightContainer) {
        // Find all section elements in the left container
        const sections = leftContainer.querySelectorAll('section');

        // Move each section to the right container
        sections.forEach(section => {
            rightContainer.appendChild(section);
        });
    }
});
</script>

{{/post}}