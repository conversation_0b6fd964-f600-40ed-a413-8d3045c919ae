{"name":"Log","hostname":"<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>-Pro-2.local","pid":98655,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"1a9f0cb7-665a-4ec4-8941-a45ef31df81a","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-vYptsnZ1XyHwUCI8374XAPNRJKE\""},"statusCode":403,"responseTime":"629ms"},"err":{"id":"d6bb9f70-6263-11f0-8fe0-956ae3b557a3","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:42:19.244Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":98655,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"b55f9451-837f-4242-8fe2-70512dcc821a","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-i820zuhXZ/PvbG/cDY5mAF7Qbzo\""},"statusCode":403,"responseTime":"3ms"},"err":{"id":"d6bdc250-6263-11f0-8fe0-956ae3b557a3","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:42:19.255Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","err":{"id":"d2fcd7d0-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":"ENOENT","name":"NotFoundError","statusCode":404,"level":"normal","message":"The currently active theme \"ease\" is missing.","context":"\"name: ease\"","help":"\"path: /Users/<USER>/Desktop/thelastblockbender/content/themes/\"","stack":"Error: ENOENT: no such file or directory, stat '/Users/<USER>/Desktop/thelastblockbender/content/themes/ease'","hideStack":true},"msg":"The currently active theme \"ease\" is missing.","time":"2025-07-16T16:56:31.950Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"1a3d1af0-d62b-4aa9-ad16-ef985a27efe2","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-2FL97/BzXMXXKE6gm0sZXIz9elA\""},"statusCode":403,"responseTime":"105ms"},"err":{"id":"d5d28d10-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:56:36.709Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"e0aa7d95-78dd-4708-a688-4e0841f6081b","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-mskQrrE3lKUZpN+hmzgLzH7IopU\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"d5de4ce0-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:55:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:56:36.787Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"19549ad0-3ef3-474a-a847-5e930ffe5bc0","userId":"1"},"url":"/session","method":"POST","originalUrl":"/ghost/api/admin/session","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","content-length":"66","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/plain, */*; q=0.01","content-type":"application/json;charset=UTF-8","origin":"http://localhost:2368","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","content-length":"339","etag":"W/\"153-OoaAFUD9P/hot8oo8bXxTnDq+aA\"","set-cookie":"**REDACTED**"},"statusCode":500,"responseTime":"458ms"},"err":{"id":"ddbaa120-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"EmailError","statusCode":500,"level":"normal","message":"Failed to send email. Please check your site configuration and try again.","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"EmailError: Failed to send email. Please check your site configuration and try again.\n    at Object.sendAuthCodeToUser (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/session-service.js:284:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Object.createSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:16:17)","hideStack":false},"msg":"Failed to send email. Please check your site configuration and try again.","time":"2025-07-16T16:56:49.982Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"aa4cfb7e-fca2-4129-a499-698288a1597e","userId":"1"},"url":"/session","method":"POST","originalUrl":"/ghost/api/admin/session","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","content-length":"66","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/plain, */*; q=0.01","content-type":"application/json;charset=UTF-8","origin":"http://localhost:2368","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","content-length":"339","etag":"W/\"153-WwCl5vM06AhvbouMhFC3A+UeNpc\""},"statusCode":500,"responseTime":"358ms"},"err":{"id":"e0d99460-6265-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"EmailError","statusCode":500,"level":"normal","message":"Failed to send email. Please check your site configuration and try again.","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"EmailError: Failed to send email. Please check your site configuration and try again.\n    at Object.sendAuthCodeToUser (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/session-service.js:284:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Object.createSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:16:17)","hideStack":false},"msg":"Failed to send email. Please check your site configuration and try again.","time":"2025-07-16T16:56:55.212Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"87ac1989-b49f-49b3-99cf-872f1d070c8c","userId":null},"url":"/users/me/?include=roles","method":"GET","originalUrl":"/ghost/api/admin/users/me/?include=roles","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"include":"roles"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"343","etag":"W/\"157-v+E2E0gwTdNTGR+ZZgCvlf50E80\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"0088b1b0-6266-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated user or integration. Check that cookies are being passed through if using session authentication.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeAdminApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:33:25)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at authenticate (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:48:28)","hideStack":false},"msg":"Authorization failed","time":"2025-07-16T16:57:48.365Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":4650,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"0edcd19a-9fd2-4b3a-be5c-d709f93bcbca","userId":"1"},"url":"/session","method":"POST","originalUrl":"/ghost/api/admin/session","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","content-length":"66","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/plain, */*; q=0.01","content-type":"application/json;charset=UTF-8","origin":"http://localhost:2368","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Origin, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"http://localhost:2368","content-type":"application/json; charset=utf-8","content-length":"339","etag":"W/\"153-NQVVpdrGLorRVlE2ZruGzDZHq5g\""},"statusCode":500,"responseTime":"161ms"},"err":{"id":"055491a0-6266-11f0-ad62-bfb03938e5ef","domain":"http://localhost:2368/","code":null,"name":"EmailError","statusCode":500,"level":"normal","message":"Failed to send email. Please check your site configuration and try again.","help":"\"Please see https://ghost.org/docs/config/#mail for instructions on configuring email.\"","stack":"EmailError: Failed to send email. Please check your site configuration and try again.\n    at Object.sendAuthCodeToUser (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/session-service.js:284:19)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)\n    at async Object.createSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/session/middleware.js:16:17)","hideStack":false},"msg":"Failed to send email. Please check your site configuration and try again.","time":"2025-07-16T16:57:56.410Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","err":{"id":"36ca76f0-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":"ENOENT","name":"NotFoundError","statusCode":404,"level":"normal","message":"The currently active theme \"ease\" is missing.","context":"\"name: ease\"","help":"\"path: /Users/<USER>/Desktop/thelastblockbender/content/themes/\"","stack":"Error: ENOENT: no such file or directory, stat '/Users/<USER>/Desktop/thelastblockbender/content/themes/ease'","hideStack":true},"msg":"The currently active theme \"ease\" is missing.","time":"2025-07-16T16:59:19.391Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"fb5c0ff0-74dd-4e82-918b-110543c5e9b2","userId":"1"},"url":"/themes/active/","method":"GET","originalUrl":"/ghost/api/admin/themes/active/","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","x-requested-with":"XMLHttpRequest","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"application/json, text/javascript, */*; q=0.01","content-type":"application/json; charset=UTF-8","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"241","etag":"W/\"f1-8YnB5U/2zMf+5v0UdgR8TDqj7Vo\""},"statusCode":422,"responseTime":"30ms"},"err":{"id":"3861b8c0-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":null,"name":"ValidationError","statusCode":422,"level":"normal","message":"Theme \"ease\" is not loaded and cannot be checked.","stack":"ValidationError: Theme \"ease\" is not loaded and cannot be checked.\n    at Object.getThemeErrors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/themes/validate.js:114:15)\n    at async Object.query (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/api/endpoints/themes.js:30:33)\n    at async getResponse (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:259:34)\n    at async ImplWrapper (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:264:30)\n    at async Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:70:28)","hideStack":false,"errorDetails":"\"ease\""},"msg":"Theme \"ease\" is not loaded and cannot be checked.","time":"2025-07-16T16:59:22.072Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"ed2550b9-5869-40ac-976e-2b5fca8f3ad9","userId":null},"url":"/","method":"GET","originalUrl":"/","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"text/html; charset=utf-8","etag":"W/\"4be-b29LKlm+ENG9E5RlPKQK2I44yec\"","vary":"Accept-Encoding","content-encoding":"br"},"statusCode":500,"responseTime":"173ms"},"err":{"id":"3c787980-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":null,"name":"InternalServerError","statusCode":500,"level":"critical","message":"The currently active theme \"ease\" is missing.","stack":"InternalServerError: The currently active theme \"ease\" is missing.\n    at ensureActiveTheme (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/frontend/services/theme-engine/middleware/ensure-active-theme.js:19:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at trim_prefix (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:328:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:286:9\n    at Function.process_params (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/index.js:280:10)\n    at loadMemberSession (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/members/middleware.js:97:9)","hideStack":false},"msg":"The currently active theme \"ease\" is missing.","time":"2025-07-16T16:59:29.090Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":5256,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"e8edce5f-1d41-466f-8950-16276da65e3f","userId":"1"},"url":"/themes/active/","method":"GET","originalUrl":"/ghost/api/admin/themes/active/","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","x-ghost-version":"5.130","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Google Chrome\";v=\"137\", \"Chromium\";v=\"137\", \"Not/A)Brand\";v=\"24\"","app-pragma":"no-cache","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/ghost/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","content-type":"application/json; charset=utf-8","content-length":"241","etag":"W/\"f1-n1HluIcd0mjK+Pf8NwdfigYRN4s\""},"statusCode":422,"responseTime":"18ms"},"err":{"id":"4b1b9350-6266-11f0-ad8e-0724a9a4ce72","domain":"http://localhost:2368/","code":null,"name":"ValidationError","statusCode":422,"level":"normal","message":"Theme \"ease\" is not loaded and cannot be checked.","stack":"ValidationError: Theme \"ease\" is not loaded and cannot be checked.\n    at Object.getThemeErrors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/themes/validate.js:114:15)\n    at async Object.query (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/api/endpoints/themes.js:30:33)\n    at async getResponse (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:259:34)\n    at async ImplWrapper (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/pipeline.js:264:30)\n    at async Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:70:28)","hideStack":false,"errorDetails":"\"ease\""},"msg":"Theme \"ease\" is not loaded and cannot be checked.","time":"2025-07-16T16:59:53.479Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":80546,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Timeout awaiting 'request' for 1000ms","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-07-29T18:40:48.395Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":80546,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Worker for job \"update-check\" exited with code 1","stack":"Error: Worker for job \"update-check\" exited with code 1\n    at Worker.<anonymous> (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/bree/lib/index.js:419:40)\n    at Worker.emit (node:events:518:28)\n    at [kOnExit] (node:internal/worker:315:10)\n    at Worker.<computed>.onexit (node:internal/worker:229:20)"},"msg":"Worker for job \"update-check\" exited with code 1","time":"2025-07-29T18:40:48.401Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":80546,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","code":"ETIMEDOUT","message":"Timeout awaiting 'request' for 1000ms","context":"\"Checking for updates failed, your site will continue to function.\"","help":"\"If you get this error repeatedly, please seek help from https://ghost.org/docs/\"","stack":"RequestError: Timeout awaiting 'request' for 1000ms\n    at ClientRequest.<anonymous> (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/index.js:792:61)\n    at Object.onceWrapper (node:events:633:26)\n    at ClientRequest.emit (node:events:530:35)\n    at emitErrorEvent (node:_http_client:101:11)\n    at TLSSocket.socketErrorListener (node:_http_client:504:5)\n    at TLSSocket.emit (node:events:518:28)\n    at emitErrorNT (node:internal/streams/destroy:169:8)\n    at emitErrorCloseNT (node:internal/streams/destroy:128:3)\n    at process.processTicksAndRejections (node:internal/process/task_queues:82:21)\n    at Timeout.timeoutHandler [as _onTimeout] (file:///Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/request/node_modules/got/dist/source/core/timed-out.js:42:25)\n    at listOnTimeout (node:internal/timers:583:11)\n    at process.processTimers (node:internal/timers:519:7)"},"msg":"Timeout awaiting 'request' for 1000ms","time":"2025-07-30T20:12:50.259Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7ad650-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.680Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7b4b80-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.681Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7b99a0-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.682Z","v":0}
{"name":"Log","hostname":"Chriss-MacBook-Pro-2.local","pid":20519,"level":50,"version":"5.130.0","err":{"id":"5e7b99a1-6d82-11f0-8b42-d93517d6d0cb","domain":"http://localhost:2368/","code":"IMAGE_SIZE_URL","name":"InternalServerError","statusCode":500,"level":"critical","message":"Probe unresponsive.","stack":"InternalServerError: Probe unresponsive.\n    at Timeout._onTimeout (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/lib/image/ImageSize.js:91:25)\n    at listOnTimeout (node:internal/timers:581:17)\n    at process.processTimers (node:internal/timers:519:7)","hideStack":false},"msg":"Probe unresponsive.","time":"2025-07-30T20:18:34.683Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":57905,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"10a038d4-3ed5-4af8-897a-f12fc4d2212b","userId":null},"url":"/search-index/posts","method":"GET","originalUrl":"/ghost/api/content/search-index/posts","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-rOeSNkSya1pQHjfmVXi4uDQDOAU\""},"statusCode":403,"responseTime":"11ms"},"err":{"id":"e7240570-6dd3-11f0-b278-4b7f16d54b0e","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:02:13.201Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":59159,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:04:17.321Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":59967,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:08:53.044Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":60343,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:10:52.435Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":61154,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"daccf243-a7c1-47b8-af7a-017259900e4d","userId":null},"url":"/search-index/pages","method":"GET","originalUrl":"/ghost/api/content/search-index/pages","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-pVQth0y4o5L/iB4B2sWLS596jao\""},"statusCode":403,"responseTime":"550ms"},"err":{"id":"b5db11a0-6dd5-11f0-932d-a53b20271096","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:15:09.517Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":61154,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"9996dc14-df8e-43b8-ae01-cd2a2b5ebccc","userId":null},"url":"/search-index/pages","method":"GET","originalUrl":"/ghost/api/content/search-index/pages","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","cache-control":"max-age=0","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","sec-ch-ua-platform":"\"macOS\"","upgrade-insecure-requests":"1","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","accept":"text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7","sec-fetch-site":"none","sec-fetch-mode":"navigate","sec-fetch-user":"?1","sec-fetch-dest":"document","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-mzuCYibm0D5ReHU+HcmyxDeC4+Q\""},"statusCode":403,"responseTime":"4ms"},"err":{"id":"b92114e0-6dd5-11f0-932d-a53b20271096","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:15:14.991Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":61154,"level":50,"version":"5.130.0","err":{"domain":"http://localhost:2368/","message":"Cannot read properties of undefined (reading 'options')","stack":"TypeError: Cannot read properties of undefined (reading 'options')\n    at Http (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/@tryghost/api-framework/lib/http.js:65:30)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at uncapitalise (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/uncapitalise.js:60:5)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at slashes (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/connect-slashes/lib/connect-slashes.js:81:9)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at Object.urlRedirectsRedirect [as redirect] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:104:5)\n    at adminRedirect (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/web/shared/middleware/url-redirects.js:112:14)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at cors (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:188:7)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:224:17\n    at originCallback (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/cors/lib/index.js:214:15)"},"msg":"Unhandled rejection: Cannot read properties of undefined (reading 'options')","time":"2025-07-31T06:15:51.536Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"7bde38b7-7096-4efd-80d2-a58dd620eb29","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-UgZn8PCHKBDlfw2Ech4T/gPhrZM\""},"statusCode":403,"responseTime":"82ms"},"err":{"id":"d897c7a0-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:17.276Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"1765b791-fa4e-4e5c-893a-37a556f6bad9","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-7NaR1fFXGV56LZ6Ei07PdnlTzkw\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"d8f43f80-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:17.885Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"65083183-8a68-44e3-b00e-a15e18677c8e","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-/uXmyqY138JD5JhBrhLKHqC/uvY\""},"statusCode":403,"responseTime":"5ms"},"err":{"id":"d9956180-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:18.941Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":62795,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"671ce396-8209-44d1-9a51-a23d318aaa4b","userId":null},"url":"/search-index/pages?key=","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"374","etag":"W/\"176-GnwM3ZeNp5fDv7XMrEQaZzW65XQ\""},"statusCode":403,"responseTime":"3ms"},"err":{"id":"da455090-6dd6-11f0-90b3-d599df7da777","domain":"http://localhost:2368/","code":null,"name":"NoPermissionError","statusCode":403,"level":"normal","message":"Authorization failed","context":"\"Unable to determine the authenticated member or integration. Check the supplied Content API Key and ensure cookies are being passed through if member auth is failing.\"","stack":"NoPermissionError: Authorization failed\n    at authorizeContentApi (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/authorize.js:20:21)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express/lib/router/route.js:149:13)\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/members/index.js:50:28\n    at /Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:118:55\n    at step (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:33:23)\n    at Object.next (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:14:53)\n    at fulfilled (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/node_modules/express-jwt/dist/index.js:5:58)\n    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)","hideStack":false},"msg":"Authorization failed","time":"2025-07-31T06:23:20.091Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":63308,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"3cd5eda9-2e75-4ab7-a9e2-2df73e17d769","userId":null},"url":"/search-index/pages?key=22444f78447824223cefc48062","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=22444f78447824223cefc48062","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","pragma":"no-cache","cache-control":"no-cache","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-NXM8N3Tq9zXLjeHghu09TdarBVc\""},"statusCode":401,"responseTime":"93ms"},"err":{"id":"347dc7e0-6dd7-11f0-85e5-118fb28a23a6","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-07-31T06:25:51.455Z","v":0}
{"name":"Log","hostname":"Chriss-MBP-2","pid":63308,"level":50,"version":"5.130.0","req":{"meta":{"requestId":"9162f311-01e9-4f65-ba58-be351e277ff2","userId":null},"url":"/search-index/pages?key=22444f78447824223cefc48062","method":"GET","originalUrl":"/ghost/api/content/search-index/pages?key=22444f78447824223cefc48062","params":{},"headers":{"host":"localhost:2368","connection":"keep-alive","pragma":"no-cache","cache-control":"no-cache","sec-ch-ua-platform":"\"macOS\"","user-agent":"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36","sec-ch-ua":"\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"","sec-ch-ua-mobile":"?0","accept":"*/*","sec-fetch-site":"same-origin","sec-fetch-mode":"cors","sec-fetch-dest":"empty","referer":"http://localhost:2368/","accept-encoding":"gzip, deflate, br, zstd","accept-language":"en-US,en;q=0.9,da;q=0.8,it;q=0.7,la;q=0.6,fr;q=0.5","cookie":"**REDACTED**"},"query":{"key":"**REDACTED**"}},"res":{"_headers":{"x-powered-by":"Express","content-version":"v5.130","vary":"Accept-Version, Accept-Encoding","cache-control":"no-cache, private, no-store, must-revalidate, max-stale=0, post-check=0, pre-check=0","access-control-allow-origin":"*","content-type":"application/json; charset=utf-8","content-length":"234","etag":"W/\"ea-mhFWd3U4ds5Vm+iEnXFz8YX+v1k\""},"statusCode":401,"responseTime":"3ms"},"err":{"id":"347e1600-6dd7-11f0-85e5-118fb28a23a6","domain":"http://localhost:2368/","code":"UNKNOWN_CONTENT_API_KEY","name":"UnauthorizedError","statusCode":401,"level":"normal","message":"Unknown Content API Key","stack":"UnauthorizedError: Unknown Content API Key\n    at authenticateContentApiKey (/Users/<USER>/Desktop/thelastblockbender/versions/5.130.0/core/server/services/auth/api-key/content.js:31:25)","hideStack":false},"msg":"Unknown Content API Key","time":"2025-07-31T06:25:51.456Z","v":0}
