<!DOCTYPE html>
<html ⚡ lang="{{@site.locale}}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width,minimum-scale=1,initial-scale=1">

    <title>{{meta_title}}</title>

    {{amp_ghost_head}}

    <style amp-custom>
    *,
    *::before,
    *::after {
        box-sizing: border-box;
    }

    html {
        overflow-x: hidden;
        overflow-y: scroll;
        font-size: 62.5%;
        -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
    }

    body {
        min-height: 100vh;
        margin: 0;
        padding: 0;
        color: #3a4145;
        font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;
        font-size: 1.7rem;
        line-height: 1.55em;
        font-weight: 400;
        font-style: normal;
        background: #fff;
        scroll-behavior: smooth;
        overflow-x: hidden;
        -webkit-font-smoothing: antialiased;
        -moz-osx-font-smoothing: grayscale;
    }

    p,
    ul,
    ol,
    li,
    dl,
    dd,
    hr,
    pre,
    form,
    table,
    video,
    figure,
    figcaption,
    blockquote {
        margin: 0;
        padding: 0;
    }

    ul[class],
    ol[class] {
        padding: 0;
        list-style: none;
    }

    img {
        display: block;
        max-width: 100%;
    }

    input,
    button,
    select,
    textarea {
        font: inherit;
        -webkit-appearance: none;
    }

    fieldset {
        margin: 0;
        padding: 0;
        border: 0;
    }

    label {
        display: block;
        font-size: 0.9em;
        font-weight: 700;
    }

    hr {
        position: relative;
        display: block;
        width: 100%;
        height: 1px;
        border: 0;
        border-top: 1px solid currentcolor;
        opacity: 0.1;
    }

    ::selection {
        text-shadow: none;
        background: #cbeafb;
    }

    mark {
        background-color: #fdffb6;
    }

    small {
        font-size: 80%;
    }

    sub,
    sup {
        position: relative;
        font-size: 75%;
        line-height: 0;
        vertical-align: baseline;
    }
    sup {
        top: -0.5em;
    }
    sub {
        bottom: -0.25em;
    }

    ul li + li {
        margin-top: 0.6em;
    }

    a {
        color: var(--ghost-accent-color, #1292EE);
        text-decoration-skip-ink: auto;
    }

    h1,
    h2,
    h3,
    h4,
    h5,
    h6 {
        margin: 0;
        font-weight: 700;
        color: #121212;
        line-height: 1.4em;
    }

    h1 {
        font-size: 3.4rem;
        line-height: 1.1em;
    }

    h2 {
        font-size: 2.4rem;
        line-height: 1.2em;
    }

    h3 {
        font-size: 1.8rem;
    }

    h4 {
        font-size: 1.7rem;
    }

    h5 {
        font-size: 1.6rem;
    }

    h6 {
        font-size: 1.6rem;
    }

    amp-img {
        height: 100%;
        width: 100%;
        max-width: 100%;
        max-height: 100%;
    }

    amp-img img {
        object-fit: cover;
    }
    
    amp-youtube {
        height: calc(100vw / 1.78);
        width: 100vw;
        position: relative;
    }

    amp-youtube img {
        position: absolute;
    }

    .page-header {
        padding: 50px 5vmin 30px;
        text-align: center;
        font-size: 2rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .page-header a {
        color: #121212;
        font-weight: 700;
        text-decoration: none;
        font-size: 1.6rem;
        letter-spacing: -0.1px;
    }

    .post {
        max-width: 680px;
        margin: 0 auto;
    }

    .post-header {
        margin: 0 5vmin 5vmin;
        text-align: center;
    }

    .post-meta {
        margin: 1rem 0 0 0;
        text-transform: uppercase;
        color: #738a94;
        font-weight: 500;
        font-size: 1.3rem;
    }

    .post-image {
        margin: 0 0 5vmin;
    }

    .post-image img {
        display: block;
        width: 100%;
        height: auto;
    }

    .post-content {
        padding: 0 5vmin;
    }

    .post-content > * + * {
        margin-top: 1.5em;
    }

    .post-content [id]:not(:first-child) {
        margin: 2em 0 0;
    }

    .post-content > [id] + * {
        margin-top: 1rem;
    }

    .post-content [id] + .kg-card,
    .post-content blockquote + .kg-card {
        margin-top: 40px;
    }

    .post-content > ul,
    .post-content > ol,
    .post-content > dl {
        padding-left: 1.9em;
    }

    .post-content hr {
        margin-top: 40px;
    }

    .post .post-content hr + * {
        margin-top: 40px;
    }

    .post-content amp-img {
        background-color: #f8f8f8;
    }

    .post-content blockquote {
        position: relative;
        font-style: italic;
    }

    .post-content blockquote::before {
        content: "";
        position: absolute;
        left: -1.5em;
        top: 0;
        bottom: 0;
        width: 0.3rem;
        background: var(--ghost-accent-color, #1292EE);
    }

    .post-content blockquote.kg-blockquote-alt {
        font-size: 1.2em;
        font-style: italic;
        line-height: 1.6em;
        text-align: center;
        color: #738a94;
        padding: 0.75em 3em 1.25em;
    }

    .post-content blockquote.kg-blockquote-alt::before {
        display: none;
    }

    .post-content :not(.kg-card):not([id]) + .kg-card {
        margin-top: 40px;
    }

    .post-content .kg-card + :not(.kg-card) {
        margin-top: 40px;
    }

    .kg-card figcaption {
        padding: 1.5rem 1.5rem 0;
        text-align: center;
        font-weight: 500;
        font-size: 1.3rem;
        line-height: 1.4em;
        opacity: 0.6;
    }

    .kg-card figcaption strong {
        color: rgba(0,0,0,0.8);
    }

    .post-content :not(pre) code {
        vertical-align: middle;
        padding: 0.15em 0.4em 0.15em;
        border: #e1eaef 1px solid;
        font-weight: 400;
        font-size: 0.9em;
        line-height: 1em;
        color: #15171a;
        background: #f0f6f9;
        border-radius: 0.25em;
    }

    .post-content > pre {
        overflow: scroll;
        padding: 16px 20px;
        color: #fff;
        background: #1F2428;
        border-radius: 5px;
        box-shadow: 0 2px 6px -2px rgba(0,0,0,.1), 0 0 1px rgba(0,0,0,.4);
    }

    .kg-embed-card {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 100%;
    }

    .kg-image-card img {
        margin: auto;
    }

    .kg-gallery-card + .kg-gallery-card {
        margin-top: 0.75em;
    }

    .kg-gallery-container {
        position: relative;
    }

    .kg-gallery-row {
        display: flex;
        flex-direction: row;
        justify-content: center;
    }

    .kg-gallery-image {
        width: 100%;
        height: 100%;
    }

    .kg-gallery-row:not(:first-of-type) {
        margin: 0.75em 0 0 0;
    }

    .kg-gallery-image:not(:first-of-type) {
        margin: 0 0 0 0.75em;
    }

    .kg-bookmark-card,
    .kg-bookmark-publisher {
        position: relative;
    }

    .kg-bookmark-container,
    .kg-bookmark-container:hover {
        display: flex;
        flex-wrap: wrap;
        flex-direction: row-reverse;
        color: currentColor;
        background: rgba(255,255,255,0.6);
        font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;
        text-decoration: none;
        border-radius: 3px;
        box-shadow: 0 2px 6px -2px rgba(0, 0, 0, 0.1), 0 0 1px rgba(0, 0, 0, 0.4);
        overflow: hidden;
    }

    .kg-bookmark-content {
        flex-basis: 0;
        flex-grow: 999;
        padding: 20px;
        order: 1;
    }

    .kg-bookmark-title {
        font-weight: 600;
        font-size: 1.5rem;
        line-height: 1.3em;
    }

    .kg-bookmark-description {
        display: -webkit-box;
        max-height: 45px;
        margin: 0.5em 0 0 0;
        font-size: 1.4rem;
        line-height: 1.55em;
        overflow: hidden;
        opacity: 0.8;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
    }

    .kg-bookmark-metadata {
        margin-top: 20px;
    }

    .kg-bookmark-metadata {
        display: flex;
        align-items: center;
        font-weight: 500;
        font-size: 1.3rem;
        line-height: 1.3em;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    .kg-bookmark-description {
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
    }

    .kg-bookmark-metadata amp-img {
        width: 18px;
        height: 18px;
        max-width: 18px;
        max-height: 18px;
        margin-right: 10px;
    }

    .kg-bookmark-thumbnail {
        display: flex;
        flex-basis: 20rem;
        flex-grow: 1;
        justify-content: flex-end;
    }

    .kg-bookmark-thumbnail amp-img {
        max-height: 200px;
    }

    .kg-bookmark-author {
        white-space: nowrap;
        text-overflow: ellipsis;
        overflow: hidden;
    }

    .kg-bookmark-publisher::before {
        content: "•";
        margin: 0 .5em;
    }

    .kg-toggle-card-icon {
        display: none;
    }

    .kg-toggle-content {
        margin-top: 0.8rem;
    }

    .kg-product-card-container {
        background: transparent;
        padding: 20px;
        width: 100%;
        border-radius: 5px;
        box-shadow: inset 0 0 0 1px rgb(124 139 154 / 25%);
    }

    .kg-product-card-description p {
        margin-top: 1.5em;
    }

    .kg-product-card-description ul {
        margin-left: 24px;
    }

    .kg-product-card-title {
        font-size: 1.9rem;
        font-weight: 700;
    }

    .kg-product-card-rating-star {
        height: 28px;
        width: 20px;
        margin-right: 2px;
    }

    .kg-product-card-rating-star svg {
    width: 16px;
    height: 16px;
    fill: currentColor;
    opacity: 0.15;
    }

    .kg-product-card-rating-active.kg-product-card-rating-star svg {
    opacity: 1;
    }

    .kg-nft-card-container {
        position: relative;
        display: flex;
        flex: auto;
        flex-direction: column;
        text-decoration: none;
        font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen,Ubuntu,Cantarell,Open Sans,Helvetica Neue,sans-serif;
        font-size: 1.4rem;
        font-weight: 400;
        box-shadow: 0 2px 6px -2px rgb(0 0 0 / 10%), 0 0 1px rgb(0 0 0 / 40%);
        width: 100%;
        max-width: 512px;
        color: #15212A;
        background: #fff;
        border-radius: 5px;
        transition: none;
        margin: 0 auto;
    }

    .kg-nft-metadata {
        padding: 2.0rem;
    }

    .kg-nft-image-container {
        position: relative;
    }

    .kg-nft-image {
        display: flex;
        border-radius: 5px 5px 0 0;
    }

    .kg-nft-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        gap: 20px;
    }

    .kg-nft-header h4.kg-nft-title {
        font-size: 1.9rem;
        font-weight: 700;
        margin: 0;
        color: #15212A;
    }

    .kg-nft-header amp-img {
        max-width: 114px;
        max-height: 26px;
    }

    .kg-nft-opensea-logo {
        margin-top: 2px;
        width: 100px;
    }

    .kg-nft-creator {
        font-family: inherit;
        color: #95A1AD;
    }

    .kg-nft-creator span {
        font-weight: 500;
        color: #15212A;
    }

    .kg-nft-card p.kg-nft-description {
        font-size: 1.4rem;
        line-height: 1.4em;
        margin: 2.0rem 0 0;
        color: #222;
    }

    .kg-button-card {
        display: flex;
        position: static;
        align-items: center;
        width: 100%;
        justify-content: center;
    }

    .kg-btn {
        display: flex;
        position: static;
        align-items: center;
        padding: 0 2.0rem;
        height: 4.0rem;
        line-height: 4.0rem;
        font-size: 1.65rem;
        font-weight: 600;
        text-decoration: none;
        border-radius: 5px;
        transition: opacity 0.2s ease-in-out;
    }

    .kg-btn:hover {
        opacity: 0.85;
    }

    .kg-btn-accent {
        background-color: var(--ghost-accent-color, #1292EE);
        color: #fff;
    }

    .kg-callout-card {
        display: flex;
        padding: 20px 28px;
        border-radius: 3px;
    }

    .kg-callout-card-grey {
        background: rgba(124, 139, 154, 0.13);
    }

    .kg-callout-card-white {
        background: transparent;
        box-shadow: inset 0 0 0 1px rgba(124, 139, 154, 0.25);
    }

    .kg-callout-card-blue {
        background: rgba(33, 172, 232, 0.12);
    }

    .kg-callout-card-green {
        background: rgba(52, 183, 67, 0.12);
    }

    .kg-callout-card-yellow {
        background: rgba(240, 165, 15, 0.13);
    }

    .kg-callout-card-red {
        background: rgba(209, 46, 46, 0.11);
    }

    .kg-callout-card-pink {
        background: rgba(225, 71, 174, 0.11);
    }

    .kg-callout-card-purple {
        background: rgba(135, 85, 236, 0.12);
    }

    .kg-callout-card-accent {
        background: var(--ghost-accent-color);
        color: #fff;
    }

    .kg-callout-card-accent a {
        color: #fff;
    }

    .kg-callout-emoji {
        padding-right: 16px;
        line-height: 1.3;
        font-size: 1.25em;
    }

    .kg-header-card {
        padding: 6em 3em;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
    }

    .kg-header-card.kg-size-small {
        padding-top: 4em;
        padding-bottom: 4em;
    }

    .kg-header-card.kg-size-large {
        padding-top: 12em;
        padding-bottom: 12em;
    }

    .kg-header-card.kg-width-full {
        padding-left: 4em;
        padding-right: 4em;
    }

    .kg-header-card.kg-align-left {
        text-align: left;
        align-items: flex-start;
    }

    .kg-header-card.kg-style-dark {
        background: #15171a;
        color: #ffffff;
    }

    .kg-header-card.kg-style-light {
        color: #15171a;
        border: 1px solid rgba(124, 139, 154, 0.25);
        border-width: 1px 0;
    }

    .kg-header-card.kg-style-accent {
        background-color: var(--ghost-accent-color);
    }

    .kg-header-card.kg-style-image {
        background-color: #e7e7eb;
        background-size: cover;
        background-position: center center;
    }

    .kg-header-card h2 {
        font-size: 4em;
        font-weight: 700;
        line-height: 1.1em;
        margin: 0;
    }

    .kg-header-card h2 strong {
        font-weight: 800;
    }

    .kg-header-card.kg-size-small h2 {
        font-size: 3em;
    }

    .kg-header-card.kg-size-large h2 {
        font-size: 5em;
    }

    .kg-header-card h3 {
        font-size: 1.25em;
        font-weight: 500;
        line-height: 1.3em;
        margin: 0;
    }

    .kg-header-card h3 strong {
        font-weight: 600;
    }

    .kg-header-card.kg-size-small h3 {
        font-size: 1em;
    }

    .kg-header-card.kg-size-large h3 {
        font-size: 1.5em;
    }

    .kg-header-card:not(.kg-style-light) h2,
    .kg-header-card:not(.kg-style-light) h3 {
        color: #ffffff;
    }

    .kg-header-card a.kg-header-card-button {
        display: flex;
        position: static;
        align-items: center;
        padding: 0 1.2em;
        height: 2.4em;
        line-height: 1em;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        font-size: 0.95em;
        font-weight: 600;
        text-decoration: none;
        border-radius: 5px;
        transition: opacity 0.2s ease-in-out;
        background-color: var(--ghost-accent-color);
        color: #ffffff;
        margin: 1.75em 0 0;
    }

    .kg-header-card a.kg-header-card-button:hover {
        opacity: 0.85;
    }

    .kg-header-card.kg-size-large a.kg-header-card-button {
        margin-top: 2em;
    }

    .kg-header-card.kg-size-small a.kg-header-card-button {
        margin-top: 1.5em;
    }

    .kg-header-card.kg-style-image a.kg-header-card-button,
    .kg-header-card.kg-style-dark a.kg-header-card-button {
        background: #ffffff;
        color: #15171a;
    }

    .kg-header-card.kg-style-accent a.kg-header-card-button {
        background: #ffffff;
        color: var(--ghost-accent-color);
    }

    .kg-audio-card {
        display: flex;
        width: 100%;
        box-shadow: inset 0 0 0 1px rgba(124, 139, 154, 0.25);
    }

    .kg-audio-thumbnail {
        display: flex;
        justify-content: center;
        align-items: center;
        width: 80px;
        min-width: 80px;
        height: 80px;
        background: transparent;
        object-fit: cover;
        aspect-ratio: 1/1;
        border-radius: 3px 0 0 3px;
    }

    .kg-audio-thumbnail.placeholder {
        background: var(--ghost-accent-color);
    }

    .kg-audio-thumbnail.placeholder svg {
        width: 24px;
        height: 24px;
        fill: white;
    }

    .kg-audio-player-container {
        position: relative;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        width: 100%;
        --seek-before-width: 0%;
        --volume-before-width: 100%;
        --buffered-width: 0%;
    }

    .kg-audio-title {
        width: 100%;
        padding: 8px 12px 0;
        border: none;
        font-family: inherit;
        font-size: 1.1em;
        font-weight: 700;
        background: transparent;
    }

    .kg-audio-player {
        display: none;
    }

    .kg-width-full.kg-card-hascaption {
        display: grid;
        grid-template-columns: inherit;
    }

    .post-content table {
        border-collapse: collapse;
        width: 100%;
    }

    .post-content th {
        padding: 0.5em 0.8em;
        text-align: left;
        font-size: .75em;
        text-transform: uppercase;
    }

    .post-content td {
        padding: 0.4em 0.7em;
    }

    .post-content tbody tr:nth-child(2n + 1) {
        background-color: rgba(0,0,0,0.1);
        padding: 1px;
    }

    .post-content tbody tr:nth-child(2n + 2) td:last-child {
        box-shadow:
            inset 1px 0 rgba(0,0,0,0.1),
            inset -1px 0 rgba(0,0,0,0.1);
    }

    .post-content tbody tr:nth-child(2n + 2) td {
        box-shadow: inset 1px 0 rgba(0,0,0,0.1);
    }

    .post-content tbody tr:last-child {
        border-bottom: 1px solid rgba(0,0,0,.1);
    }

    .page-footer {
        padding: 60px 5vmin;
        margin: 60px auto 0;
        text-align: center;
        background-color: #f8f8f8;
    }

    .page-footer h3 {
        margin: 0.5rem 0 0 0;
    }

    .page-footer p {
        max-width: 500px;
        margin: 1rem auto 1.5rem;
        font-size: 1.7rem;
        line-height: 1.5em;
        color: rgba(0,0,0,0.6)
    }

    .powered {
        display: inline-flex;
        align-items: center;
        margin: 30px 0 0;
        padding: 6px 9px 6px 6px;
        border: rgba(0,0,0,0.1) 1px solid;
        font-size: 12px;
        line-height: 12px;
        letter-spacing: -0.2px;
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
        font-weight: 500;
        color: #222;
        text-decoration: none;
        background: #fff;
        border-radius: 6px;
    }

    .powered svg {
        height: 16px;
        width: 16px;
        margin: 0 6px 0 0;
    }

    @media (max-width: 600px) {
        body {
            font-size: 1.6rem;
        }
        h1 {
            font-size: 3rem;
        }

        h2 {
            font-size: 2.2rem;
        }
    }

    @media (max-width: 400px) {
        h1 {
            font-size: 2.6rem;
            line-height: 1.15em;
        }
        h2 {
            font-size: 2rem;
            line-height: 1.2em;
        }
        h3 {
            font-size: 1.7rem;
        }
    }

    {{amp_style}}
    </style>

    {{!-- AMP Boilerplate --}}
    <style amp-boilerplate>body{-webkit-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-moz-animation:-amp-start 8s steps(1,end) 0s 1 normal both;-ms-animation:-amp-start 8s steps(1,end) 0s 1 normal both;animation:-amp-start 8s steps(1,end) 0s 1 normal both}@-webkit-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-moz-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-ms-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@-o-keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}@keyframes -amp-start{from{visibility:hidden}to{visibility:visible}}</style><noscript><style amp-boilerplate>body{-webkit-animation:none;-moz-animation:none;-ms-animation:none;animation:none}</style></noscript>
    <script async src="https://cdn.ampproject.org/v0.js"></script>

    {{amp_components}}

</head>

<body class="amp-template">
    {{#post}}
    <header class="page-header">
        <a href="{{@site.url}}">
            {{#if @site.icon}}
                <amp-img class="site-icon" src="{{img_url @site.icon absolute="true"}}" width="50" height="50" layout="fixed" alt="{{@site.title}}"></amp-img>
            {{else}}
                {{@site.title}}
            {{/if}}
        </a>
    </header>

    <main class="content" role="main">
        <article class="post">

            <header class="post-header">
                <h1 class="post-title">{{title}}</h1>
                <section class="post-meta">
                    {{primary_author.name}} -
                    <time class="post-date" datetime="{{date format="YYYY-MM-DD"}}">{{date format="DD MMM YYYY"}}</time>
                </section>
            </header>
            {{#if feature_image}}
            <figure class="post-image">
                <amp-img src="{{img_url feature_image absolute="true"}}" width="600" height="340" layout="responsive" 
                alt="{{#if feature_image_alt}}{{feature_image_alt}}{{else}}{{title}}{{/if}}"
                ></amp-img>
            </figure>
            {{/if}}
            <section class="post-content">

                {{amp_content}}

            </section>

        </article>
    </main>
    {{/post}}
    <footer class="page-footer">
        {{#if @site.icon}}
            <amp-img class="site-icon" src="{{img_url @site.icon absolute="true"}}" width="50" height="50" layout="fixed" alt="{{@site.title}}"></amp-img>
        {{/if}}
        <h3>{{@site.title}}</h3>
        {{#if @site.description}}
            <p>{{@site.description}}</p>
        {{/if}}
        <p><a href="{{@site.url}}">Read more posts →</a></p>
        <a class="powered" href="https://ghost.org" target="_blank" rel="noopener"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 156 156"><g fill="none" fill-rule="evenodd"><rect fill="#15212B" width="156" height="156" rx="27"/><g transform="translate(36 36)" fill="#F6F8FA"><path d="M0 71.007A4.004 4.004 0 014 67h26a4 4 0 014 4.007v8.986A4.004 4.004 0 0130 84H4a4 4 0 01-4-4.007v-8.986zM50 71.007A4.004 4.004 0 0154 67h26a4 4 0 014 4.007v8.986A4.004 4.004 0 0180 84H54a4 4 0 01-4-4.007v-8.986z"/><rect y="34" width="84" height="17" rx="4"/><path d="M0 4.007A4.007 4.007 0 014.007 0h41.986A4.003 4.003 0 0150 4.007v8.986A4.007 4.007 0 0145.993 17H4.007A4.003 4.003 0 010 12.993V4.007z"/><rect x="67" width="17" height="17" rx="4"/></g></g></svg> Published with Ghost</a>
    </footer>
    {{amp_analytics}}
</body>
</html>
