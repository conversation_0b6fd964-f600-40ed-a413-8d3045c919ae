"use strict";(()=>{var f=Object.defineProperty;var R=Object.getOwnPropertyDescriptor;var S=Object.getOwnPropertyNames;var M=Object.prototype.hasOwnProperty;var _=(t,r)=>()=>(t&&(r=t(t=0)),r);var U=(t,r)=>{for(var i in r)f(t,i,{get:r[i],enumerable:!0})},x=(t,r,i,e)=>{if(r&&typeof r=="object"||typeof r=="function")for(let n of S(r))!M.call(t,n)&&n!==i&&f(t,n,{get:()=>r[n],enumerable:!(e=R(r,n))||e.enumerable});return t};var A=t=>x(f({},"__esModule",{value:!0}),t);var p={};U(p,{getFinalReferrer:()=>g,getReferrer:()=>I,parsePortalHash:()=>d,parseReferrer:()=>h});function h(t){let r=new URL(t||window.location.href),i=r.searchParams.get("ref"),e=r.searchParams.get("source"),n=r.searchParams.get("utm_source"),a=r.searchParams.get("utm_medium"),s=i||e||n||null;return!s&&r.hash&&r.hash.includes("#/portal")?d(r):{source:s,medium:a||null,url:window.document.referrer||null}}function d(t){let r=new URL(t.href.replace("/#/portal","")),i=r.searchParams.get("ref"),e=r.searchParams.get("source"),n=r.searchParams.get("utm_source"),a=r.searchParams.get("utm_medium");return{source:i||e||n||null,medium:a||null,url:window.document.referrer||null}}function g(t){let{source:r,medium:i,url:e}=t,n=r||i||e||null;if(n)try{let a=new URL(n).hostname,s=window.location.hostname;if(a===s)return null}catch{return n}return n}function I(t){let r=h(t);return g(r)}var w=_(()=>{"use strict"});var y=(w(),A(p)),T=y.parseReferrer,E=y.getReferrer,P="ghost-history",L=24*60*60*1e3,b=15;(async function(){try{let t=window.localStorage,r=t.getItem(P),i=new Date().getTime(),e=[];if(r)try{e=JSON.parse(r)}catch(o){console.warn("[Member Attribution] Error while parsing history",o)}let n=e.findIndex(o=>{if(!o.time||typeof o.time!="number")return!1;let c=i-o.time;return!(isNaN(o.time)||c>L)});n>0?e.splice(0,n):n===-1&&(e=[]);let a;try{a=T(window.location.href)}catch(o){console.error("[Member Attribution] Parsing referrer failed",o),a={source:null,medium:null,url:null}}let s=a.source,l=a.medium,u;try{u=E(window.location.href),!u&&a.url&&(u=a.url)}catch(o){console.error("[Member Attribution] Getting final referrer failed",o),u=a.url}try{let o=new URL(window.location.href),c=o.searchParams;c.get("attribution_id")&&c.get("attribution_type")&&(e.push({time:i,id:c.get("attribution_id"),type:c.get("attribution_type"),referrerSource:s,referrerMedium:l,referrerUrl:u}),c.delete("attribution_id"),c.delete("attribution_type"),o.search="?"+c.toString(),window.history.replaceState({},"",`${o.pathname}${o.search}${o.hash}`))}catch(o){console.error("[Member Attribution] Parsing attribution from querystring failed",o)}let m=window.location.pathname;e.length===0||e[e.length-1].path!==m?e.push({path:m,time:i,referrerSource:s,referrerMedium:l,referrerUrl:u}):e.length>0&&(e[e.length-1].time=i,s&&(e[e.length-1].referrerSource=s,e[e.length-1].referrerMedium=l),u&&(e[e.length-1].referrerUrl=u)),e.length>b&&(e=e.slice(-b)),t.setItem(P,JSON.stringify(e))}catch(t){console.error("[Member Attribution] Failed with error",t)}})();})();
