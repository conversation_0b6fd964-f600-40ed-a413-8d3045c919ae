.kg-cta-card,
.kg-cta-card * {
    box-sizing: border-box;
}

.kg-cta-card {
    display: flex;
    flex-direction: column;
    border-radius: 8px;
}

.kg-cta-bg-grey {
    background: rgba(151, 163, 175, 0.14)
}

.kg-cta-bg-white {
    background: transparent;
    box-shadow: inset 0 0 0 1px rgba(124, 139, 154, 0.2);
}

.kg-cta-bg-blue {
    background: rgba(33, 172, 232, 0.12);
}

.kg-cta-bg-green {
    background: rgba(52, 183, 67, 0.12);
}

.kg-cta-bg-yellow {
    background: rgba(240, 165, 15, 0.13);
}

.kg-cta-bg-red {
    background: rgba(209, 46, 46, 0.11);
}

.kg-cta-bg-pink {
    background: rgba(225, 71, 174, 0.11);
}

.kg-cta-bg-purple {
    background: rgba(135, 85, 236, 0.12);
}

.kg-cta-sponsor-label-wrapper {
    margin: 0 1.5em;
    padding: .7em 0;
    border-bottom: 1px solid rgba(124, 139, 154, 0.2);
}

@media (max-width: 600px) {
    .kg-cta-sponsor-label-wrapper {
        margin: 0 1.25em;
        padding: .5em 0;
    }
}

.kg-cta-bg-none .kg-cta-sponsor-label-wrapper {
    margin: 0;
    padding-top: 0;
}

.kg-cta-has-img .kg-cta-sponsor-label-wrapper:not(.kg-cta-bg-none .kg-cta-sponsor-label-wrapper):not(.kg-cta-minimal .kg-cta-sponsor-label-wrapper),
.kg-cta-bg-none.kg-cta-no-dividers .kg-cta-sponsor-label-wrapper {
    border-bottom: 0;
}

.kg-cta-sponsor-label {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    font-size: 12px;
    font-weight: 600;
    text-transform: uppercase;
    text-wrap: pretty;
}

.kg-cta-sponsor-label span:not(a span) {
    color: color-mix(in srgb, currentColor 45%, transparent);
}

.kg-cta-sponsor-label a,
.kg-cta-sponsor-label a span {
    color: currentColor;
    transition: opacity 0.15s ease-in-out;
}

.kg-cta-sponsor-label a:hover,
.kg-cta-sponsor-label a:hover span {
    color: currentColor;
    opacity: 0.85;
}

.kg-cta-link-accent .kg-cta-sponsor-label a {
    color: var(--ghost-accent-color);
}

.kg-cta-content {
    display: flex;
    padding: 1.5em;
    gap: 1.5em;
}

@media (max-width: 600px) {
    .kg-cta-content {
        padding: 1.25em;
        gap: 1.25em;
    }
}

.kg-cta-has-img .kg-cta-sponsor-label-wrapper + .kg-cta-content:not(.kg-cta-bg-none .kg-cta-content):not(.kg-cta-minimal .kg-cta-content) {
    padding-top: 0;
}

.kg-cta-bg-none .kg-cta-content {
    padding: 1.5em 0;
    border-bottom: 1px solid rgba(124, 139, 154, 0.2);
}

.kg-cta-bg-none.kg-cta-no-dividers .kg-cta-content {
    padding: 0;
    border-bottom: none;
}

.kg-cta-bg-none:not(.kg-cta-no-dividers) .kg-cta-content:not(.kg-cta-sponsor-label-wrapper + .kg-cta-content) {
    border-top: 1px solid rgba(124, 139, 154, 0.2);
}

@media (max-width: 600px) {
    .kg-cta-bg-none .kg-cta-content {
        padding: 1.25em 0;
    }
}

.kg-cta-minimal .kg-cta-content {
    flex-direction: row;
}

@media (max-width: 600px) {
    .kg-cta-minimal .kg-cta-content {
        flex-direction: column;
        gap: 1.6rem;
    }
}

.kg-cta-immersive .kg-cta-content {
    flex-direction: column;
}

.kg-cta-content-inner {
    display: flex;
    flex-direction: column;
    gap: 1.5em;
}

@media (max-width: 600px) {
    .kg-cta-content-inner {
        gap: 1.25em;
    }
}

.kg-cta-immersive.kg-cta-centered .kg-cta-content-inner {
    align-items: center;
}

.kg-cta-image-container {
    flex-shrink: 0;
}

.kg-cta-image-container img {
    width: 100%;
    height: auto;
    margin: 0;
    object-fit: cover;
    border-radius: 6px;
}


.kg-cta-minimal .kg-cta-image-container img {
    width: 64px;
    height: 64px;
}

@media (max-width: 600px) {
    .kg-cta-minimal .kg-cta-image-container img {
        width: 52px;
        height: 52px;
    }
}

.kg-cta-text p {
    margin: 0;
    line-height: 1.5em;
    text-wrap: pretty;
}

.kg-cta-bg-none .kg-cta-text p {
    line-height: unset;
}

.kg-cta-immersive.kg-cta-centered .kg-cta-text {
    text-align: center;
}

.kg-cta-text p + p {
    margin-top: 1.25em;
}

.kg-cta-text a {
    color: currentColor;
    transition: opacity 0.15s ease-in-out;
}

.kg-cta-text a:hover {
    color: currentColor;
    opacity: 0.85;
}

.kg-cta-link-accent .kg-cta-text a {
    color: var(--ghost-accent-color);
}

a.kg-cta-button {
    display: flex;
    position: static;
    align-items: center;
    justify-content: center;
    padding: 0 1em;
    height: 2.5em;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Oxygen", "Ubuntu", "Cantarell", "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
    font-size: 0.95em;
    font-weight: 500;
    line-height: 1.65;
    text-decoration: none;
    border-radius: 6px;
    transition: opacity 0.15s ease-in-out;
}

a.kg-cta-button:hover {
    opacity: 0.85;
}

a.kg-cta-button.kg-style-accent {
    background-color: var(--ghost-accent-color);
}

a.kg-cta-button {
    width: max-content;
}

.kg-cta-immersive.kg-cta-has-img a.kg-cta-button {
    width: 100%;
}

